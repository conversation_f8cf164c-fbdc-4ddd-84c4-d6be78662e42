import type { DC_Domain_Models_Financials_AdvancePaymentAdviceStatus } from './DC_Domain_Models_Financials_AdvancePaymentAdviceStatus';
import type { DC_Domain_Models_General_MetersCombination } from './DC_Domain_Models_General_MetersCombination';
import type { Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2Limits } from './Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2Limits';
import type { Financials_AdvancePaymentAdviceV2_AdviceStatusToggles } from './Financials_AdvancePaymentAdviceV2_AdviceStatusToggles';
import type { Financials_ForecastRange } from './Financials_ForecastRange';
export interface Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 {
  advice: number;
  current: number;
  cashBackAmount: number;
  adviceDate?: string | null;
  yearNoteDate?: string | null;
  adviceStatus: DC_Domain_Models_Financials_AdvancePaymentAdviceStatus;
  forecastRange?: Financials_ForecastRange;
  statusToggle?: Financials_AdvancePaymentAdviceV2_AdviceStatusToggles;
  limits?: Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2Limits;
  remainingTerms?: number | null;
  metersCombination: DC_Domain_Models_General_MetersCombination;
  usagesCostsTotal: number;
  alreadyPassedUsagesCostsTotal: number;
  remainingUsagesCostsTotal: number;
  advancePaymentAmountTotal: number;
  alreadyPassedAdvancePaymentAmountTotal: number;
  remainingAdvancePaymentAmountTotal: number;
  differenceUsagesCostsTotalAndAdvancePaymentAmountTotal: number;
  periodStart?: string | null;
  periodEnd?: string | null;
  increaseWillBePermanentAboveMinimumRange: boolean;
}
