import type { DC_Domain_Models_Products_ProductRates_ProductRateDetail } from './DC_Domain_Models_Products_ProductRates_ProductRateDetail';
import type { DC_Domain_Models_Products_ProductType } from './DC_Domain_Models_Products_ProductType';
export interface DC_Domain_Models_Products_ProductRates_BaseProductRate {
  productRateCode?: string | null;
  agreementId?: number | null;
  productRateDetails?: Array<DC_Domain_Models_Products_ProductRates_ProductRateDetail> | null;
  isDoubleTariff?: boolean | null;
  categorieWarmtenet?: number | null;
  koudevermogen?: number | null;
  disclaimer?: string | null;
  productType: DC_Domain_Models_Products_ProductType;
  isDynamicPricing: boolean;
  isModelContract: boolean;
  priceDifferentiationType?: string | null;
}
