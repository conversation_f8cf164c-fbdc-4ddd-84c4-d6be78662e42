# New flow library

## Contents

- [New flow library](#new-flow-library)
  - [Contents](#contents)
  - [Predecessor documentation](#predecessor-documentation)
  - [Intro](#intro)
  - [High level interaction diagram](#high-level-interaction-diagram)
  - [API changes](#api-changes)
    - [Managing flow context](#managing-flow-context)
    - [Deprecation of useFlowContext](#deprecation-of-useflowcontext)
    - [New GOTO event](#new-goto-event)
    - [Main building blocks of the root route](#main-building-blocks-of-the-root-route)
  - [Migration](#migration)
    - [Overview](#overview)
    - [Migration steps of all flows](#migration-steps-of-all-flows)
  - [Testing](#testing)
  - [Notes about API machines](#notes-about-api-machines)
  - [Resources](#resources)
  - [Additional guides](#additional-guides)

## Predecessor documentation

- [Old README](./README-old.MD)

## Intro

This library is named as **libs/eneco/flows2**, which is a simplified duplicate
of the **libs/eneco/flow** library. It contains no dependency to **xstate-v4**,
it makes use of the imports from **@eneco-packages/xstate-custom** instead,
which uses **xstate-v5**. The reason behind complete seperation of the library
is mainly to enable incremental migration. It might be worth to note that this
library currently is a minified duplication of the old flows. The flow/basket
logic is migrated to xstate-v5, with a slightly changed API. The missing pieces
will reveal themselves, if any, once we start to migrate the consumers.

## High level interaction diagram

![alt text](src/images/flows-interaction-diagram.png)

## API changes

The API of the new flow library has been slightly changed, mainly driven by
separation of concerns.

### Managing flow context

How manage the flow context is changed. Formerly, there was a shared
FlowContext, which was the intersected types of all contexts per flow. The sub
contexts was stored in the library itself. That approach has the following
downsides.

1. The FlowContext containing all types leads to type collusion in different
   flows. There was no way to ensure type safety between different flows having
   the fields with the same name.
2. A particular flow has access to all fields of other flows because of the type
   intersection, which is extremely misleading.

In the new implementation, the attitude is shifted more towards the use of
inheritance instead. That approach inverts the control to the consumer flows,
each allowed to define its own context with certain fields. That makes the
boundaries clearer. So now we have a base FlowContext that is defined in the
library, and each consumer flow should extend from that FlowContext in the
consumer code, to create a dedicated context to the flow itself, with the
addition of relevant fields.

**Old code**

```tsx
import { FlowContextBoilerComfort } from './flowContextBoilerComfort';
import { flowContextCancelation } from './flowContextCancelation';
import { FlowContextContractAdvice } from './flowContextContractAdvice';
import { FlowContextDeceased } from './flowContextDeceased';
import { FlowContextDongle } from './flowContextDongle';
import { FlowContextEnergy } from './flowContextEnergy';
import { FlowContextEnergyPlan } from './flowContextEnergyPlan';
import { FlowContextHeatpumpSuitability } from './flowContextHeatpumpSuitability';
import { FlowContextLead } from './flowContextLead';
import { FlowContextLeadAdditionalQuestionnaire } from './flowContextLeadAdditionalQuestionnaire';
import { FlowContextLegslationCompass } from './flowContextLegislationCompass';
import { FlowContextPeakPriceLimit } from './flowContextPeakPriceLimit';
import { FlowContextQuoteApproval } from './flowContextQuoteApproval';
import { flowContextRentalDevice } from './flowContextRentalDevice';
import { FlowContextServiceGemak } from './flowContextServiceGemak';
import { FlowContextSme } from './flowContextSme';
import { FlowContextToon } from './flowContextToon';
import { FlowContextUrgenEnergy } from './flowContextUrgentEnergy';

export type FlowContextExtensions = FlowContextServiceGemak &
  flowContextCancelation &
  FlowContextContractAdvice &
  FlowContextBoilerComfort &
  FlowContextDongle &
  FlowContextEnergy &
  FlowContextEnergyPlan &
  FlowContextLead &
  FlowContextLeadAdditionalQuestionnaire &
  FlowContextLegslationCompass &
  FlowContextPeakPriceLimit &
  FlowContextQuoteApproval &
  flowContextRentalDevice &
  FlowContextSme &
  FlowContextToon &
  FlowContextDeceased &
  FlowContextUrgenEnergy &
  FlowContextHeatpumpSuitability;
```

**New code**

```tsx
// library
export interface FlowContext {
  basketId?: string;

  path?: string;
  pathPrevious?: string;
  pathCategory?: string;
  hasPreviousPath?: boolean;

  isAuthenticated?: boolean;
  accountId?: number | null;

  postalCode?: string;
  houseNumber?: number;
  houseNumberSuffix?: string | null;
  street?: string;
  city?: string;
}

// consumer

export interface FlowContextServiceGemak extends FlowContext {
  hasFinishedBoilerCheck?: boolean;
  centralHeatingOwnership?: boolean;
  hasBoiler?: string;
  kcMerk?: string;
  kcAsbest?: string;
  kcAccess?: boolean;
  kcStairs?: boolean;
  kcBereikbaar?: boolean;
  kcAge?: string;
  sgProduct?: string;
  kcType?: string;
  startDate?: string;
  sgDefault?: string;
  voucherCode?: string;
  offerType?: DC_Domain_Models_Products_DiscountType;
  street?: string;
  postalCode?: string;
  houseNumber?: number;
  houseNumberSuffix?: string;
  city?: string;

  salutation?: string;
  initials?: string;
  firstName?: string;
  surnamePreposition?: string;
  surname?: string;
  birthdate?: string;

  iban?: string;

  phoneNumber?: string;
  emailAddress?: string;
  isPersonalDetailsEditProhibited?: boolean;
  C1?: boolean;
  C2?: boolean;
  C3?: boolean;

  isSME?: boolean;
  offer?: Products_Offers_V3_OfferResponseModel;
}
```

How we can use an extended interface in the consumer will be explained in the
following sections.

### Deprecation of useFlowContext

The old library has leveraged a hook called **useFlowContext**, mainly to
interact with the flow/basket machines, used as follows:

```tsx
const { flowService } = useFlowContext();
const [flowState, sendToFlowMachine] = useActor(flowService);
```

It keeps the flow/basket services within a context that is accessible throughout
the flow, so that any step can use it by using the xstate hook **useActor**. The
first parameter of the array was containing the state, meanwhile the second
parameter is used to fire an event to the machine. There is no concept called
services in the new XState versions anymore.

A new **FlowProvider** is implemented, which leverages a **HooksContext** within
itself. There are helper hooks exposed from that context to interact with
**flow/basket machines**, that are accessible via the hook **useFlowHooks**. So
the **useFlowHooks** will be the new hook that should be used instead of old
**useFlowContext**, as follows:

```tsx
const { useFlowActorRef, useFlowSelector } =
  useFlowHooks<FlowContextServiceGemak>();
```

It takes a generic type parameter which is supposed to be the type of the
context, that's defined in the consumer, for the sake of type inference
[see](#managing-flow-context). It exposes the helper hooks to interact with
**flow/basket machines** to the consumer, though only the flow related ones are
more commonly used as above.

**useFlowActorRef** returns an instance of flow actor, which helps us to send
events to the flow machine via its **send** function. We could've only exposed
the **send** function instead of the actor ref, but I found it useful since
other functions of the actor can be also useful. Below is an example of how it's
used in a particular flow step:

```tsx
// destructure the hooks
const { useFlowActorRef } = useFlowHooks<FlowContextServiceGemak>();
// destructure the send function from returned actor ref
const { send } = useFlowActorRef();
// send an event to the flow machine
send({
  type: 'NEXT_PAGE',
  values: {
    exampleField: 'exampleValue',
  },
});
```

To read any value from the flow machine's state, we need to use the
**useFlowSelector** hook, that's exposed by
**useFlowHooks<T extends FlowContext>**. Below is an example of how we can use
that in any flow step:

```tsx
const { useFlowSelector } = useFlowHooks<FlowContextServiceGemak>();
const phoneNumber = useFlowSelector(state => state.context.phoneNumber);
const emailAddress = useFlowSelector(state => state.context.emailAddress);
const isPersonalDetailsEditProhibited = useFlowSelector(
  state => state.context.isPersonalDetailsEditProhibited,
);
const C1 = useFlowSelector(state => state.context.C1) || true;
const C2 = useFlowSelector(state => state.context.C2) || true;
const C3 = useFlowSelector(state => state.context.C3) || true;
```

### New GOTO event

The old implementation was creating an event per state transition. It's now
changed to a single event called **GOTO**, taking a target state id as an extra
event parameter instead.

```tsx
sendToFlowMachine('STEP_COMPANY_DETAILS');
```

is now changed to

```tsx
send({ type: 'GOTO', stepName: 'STEP_PERSONAL_DETAILS' });
```

### Main building blocks of the root route

The main building blocks that were needed to build the root flow route were
mainly **FlowProvider**, **FlowRouter** and **Navigation**. Below is an example
root using the old API.

```tsx
import { Layout, Navigation } from '@eneco/flows';
import { FlowProvider, FlowRouter } from '@eneco/flows/utils';
const Flow = () => {
  <FlowProvider
    routes={routes}
    actions={actions}
    initialContext={initialContext}>
    <Layout>
      <Layout.Header>
        <Navigation
          routes={routes}
          sitecoreComponentName="LeadFlowNavigation"
        />
      </Layout.Header>

      <FlowRouter routes={routes} />
    </Layout>
  </FlowProvider>;
};
```

In the new implementation, the responsibility of creating those components are
delegated to a flow provider factory, which simplifies the API a lot and cuts
down on passing down the unnecessary props over and over. Below is an example of
how the new API looks like:

```tsx
const { Provider, Navigation, Router } =
  createFlowProvider<FlowContextServiceGemak>({
    routes,
    actions,
    initialContext,
  });

const Flow = () => (
  <Provider>
    <Layout>
      <Layout.Header>
        <Navigation sitecoreComponentName="ServiceGemakFlowNavigation" />
      </Layout.Header>
      <Router />
    </Layout>
  </Provider>
);
```

As you can see, the required components are generated with the help of a single
factory, and there is no need to pass down the **routes** prop to the child
components, since the factory function knows about that already. The generic
argument of the factory function takes care of the type inference. Important to
note that **createFlowProvider** function should be called outside of the React
lifecycle as demonstrated above, since all of the parameters are static, and we
need a single Provider.

If you have a scenario in which you need to call the **createFlowProvider**
within React lifecycle, then result should be memoized with **useMemo**.
Otherwise, multiple calls to **createFlowProvider** on any rerender would result
in unnecessary remount of the entire tree, which breaks some functionality like
tracking.

## Migration

### Overview

This library is seperated from the old library to enable the incremental
migration, and called **libs/eneco/flows2**. It doesn't have any dependency to
the **xstate-v4**. To start migration for one particular flow, the imports that
refers to **libs/eneco/flows** should be changed to **libs/eneco/flows2**.

### Migration steps of all flows

I will share a migrated flow example, which is called ServiceGemakFlow. The
strategy for migrating the rest of the flows should be as follows:

1. Change all imports in the relevant flow from **libs/eneco/flows** to
   **libs/eneco/flows2**.
2. Create a brand new context within the flow, which has the relevant fields,
   extending from the base FlowContext, as described
   [here](#managing-flow-context)
3. Change the root route of the flow to make use of
   [flow provider factory](#main-building-blocks-of-the-root-route).
4. Change the use of **useFlowContext** to **useFlowHooks** in the flow steps,
   as described [here](#deprecation-of-useflowcontext).
5. Change any direct transition event to make use of the new **GOTO** event as
   described [here](#new-goto-event).

Note that the new library doesn't contain everything that exists in
**eneco/libs/flows**. So if you find anything missing while doing the migration,
please try to move the logic from existing/old library(**libs/eneco/flows**) to
the new one(**libs/eneco/flows2**).

The long term plan is to migrate all the flows to **libs/eneco/flows2**. Once
that's done, we can remove the current **libs/eneco/flows** folder and rename
the **libs/eneco/flows2** as **libs/eneco/flows**. We can get rid of the
**xstate-v4** dependency afterwards.

Please feel free to reach out to
[me](https://eneco-online.slack.com/team/U05R9HRAVEU), for any questions,
improvements, reviews.

## Testing

See the [utility file](./src/utils/testUtils.ts) which can be used to easily
mock machines and wait on the state updates. The **waitFor** function is to
await on the machine state updates, meanwhile **setupMachine** function is to
easily create mock machines with custom configuration, using a provided machine
as the base.

```tsx
const actor = setupMachine(
  basketMachine,
  {
    createBasket: fromPromise(({ input }) =>
      Promise.resolve({ data: newBasketData }),
    ),
  },
  'basket-machine-test',
);
actor.start();

actor.send({ type: 'CREATE_BASKET', config });
await waitFor(actor, state => state.matches('SUCCESS'));
expect(actor.getSnapshot().context.basketId).toBe(newBasketData.id);
actor.stop();
```

## Notes about API machines

In the old library version, there are a variety of API machines, which serves no
purpose, because what they basically do is handle the promise states. Flow
library shouldn't contain such dependencies. Instead of creating a machine per
API, it's wiser to use generated DC custom hooks for API requests, which already
encapsulate the promise states. See the
[createMutationHook](https://github.com/eneco-online/eneco-dxp-frontend/blob/4dae7c88f4c0d22c7adc6cb5b62659c29faf6408/libs/dc/src/client/mutation.ts)

## Resources

- [Official XState v5 Docs](https://stately.ai/docs/xstate-v5)

## Additional guides

- [State management](./state-management.md)
