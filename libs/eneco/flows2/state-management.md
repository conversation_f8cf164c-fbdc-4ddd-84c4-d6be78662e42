# State management in flows

This guide is written to describe how we handle the state throughout a
particular flow. A flow can be described as a wizard-like structure, which
consists of a set of pages, each served on different url paths. We configure the
set of steps, including id and route of each, in the configuration files per
flow. That configuration is passed to the
[flow provider factory](./README.md#main-building-blocks-of-the-root-route) to
set up the flow.

## Flow context and basket machine

We use flow context to capture the data related to the flow. That piece of data
can stem from a query string, a result of a user interaction and so on. We use
the basket machine to persist all captured data in between the flow steps. Once
any particular flow is entered, a basket with an id is created and that id is
stored in the cookie. There is no name collusion between the flows, because the
key of the cookie is using the flow name which is unique. After a basket is
created, we persist/read the state on each step change. This helps the user to
continue from where he left off, if he/she has left the flow without completing
it. Once he/she comes back, the flow would have all the data that was submitted
before. We can consider that mechanism as flow-local scope.

## Sticky query parameters

In some cases, we need to rely on the data that is passed to the flow from
outside. In those cases, the source that triggers the flow url can take over the
control and override the behaviour of the context. We can use the analogy of
environment variables to better understand this. Let me explain why the
[flow-local scope](#flow-context-and-basket-machine) wouldn't be enough by an
example:

Suppose that a flow is entered with a query parameter, and is proceeded to a
particular step. In that case, the query param is persisted in the flow. Suppose
that flow is exited at that point, and reentered at a later time, from a
location that doesn't pass a query parameter. Even though the query parameter
isn't supposed to exist at that moment, that would be still in the flow context
because it was once persistent in the basket. This situation might cause some
problems, if we have logic depending on that parameter.

To solve such problems, we can use sticky query params. A set of query param
names can be passed to the `FlowProvider` as an extra argument array, which
indicates that all steps should have those params if they're passed to the flow.
The parameters marked as sticky are propagated throughout each step. Since the
url would self preserve the state, we do not need to take extra care about
cleanup etc., and the url would contain consistent state throughout the flow
lifetime.

### Example use case

As a concrete example, suppose that we'd like to pass a query parameter as an
attribute to Growthbook, to be later used in attribute targeting. We would wrap
the flow root with `ExperimentProvider` in that case.

Sticky query parameter is configured as follows:

```tsx
const { Provider, Navigation, Router } = useMemo(
  () =>
    createFlowProvider<FlowConsumerContextEnergy>({
      routes,
      actions,
      initialContext,
      stickyQueryParams: ['excludeExperiment'],
    }),
  [routes, actions, initialContext],
);
```

And can be read as a query parameter in confidence, because it is guaranteed to
be existent throughout the flow, which means any rerender doesn't cause
inconsistent values passes to the `ExperimentProvider`.

```tsx
const Flow = () => {
  const { query: queryParams } = useRouter();
  const excludeExperiment = Boolean(queryParams.excludeExperiment);

  const attributes = useMemo(
    () => ({
      excludeExperiment,
    }),
    [excludeExperiment],
  );

  return (
    <ExperimentProvider
      testConfigs={testConfigs}
      defaultConfig={defaultConfig}
      attributes={attributes}>
      <FeaturesReady fallback={null} timeout={1000}>
        <EnergyFlow />
      </FeaturesReady>
    </ExperimentProvider>
  );
};
```
