import { createDCHook, createMutationHook, collapseDataFromCall, collapseParams } from '../client';
import {
  getAdvancePaymentNl,
  putAdvancePaymentNl,
  getAdvancePaymentBe,
  putDesiredAdvancePayment,
  getFinancialOverview,
  getFinancialPreferences,
  updateFinancialPreferences,
  downloadInvoiceDocument,
  getPaymentArrangement,
  createPaymentArrangement,
  proposePaymentArrangement,
  getAdvancePaymentAdvice,
  getAdvancePaymentAdviceV2,
  getInvoicesOverview,
  getPaymentPlan,
  getPaymentPlanBreakdown,
} from '../services/FinancialsService';

export const useFinancialsGetAdvancePaymentNl = createDCHook(
  'getAdvancePaymentNl',
  collapseDataFromCall(getAdvancePaymentNl),
  { injectables: ['label', 'customerId', 'accountId'], flattenData: true },
);

export const useFinancialsPutAdvancePaymentNl = createMutationHook(
  collapseParams(collapseParams(putAdvancePaymentNl, 'requestBody', 'data'), 'data', 'amount'),
  {
    injectables: ['label', 'customerId', 'accountId'],
    flattenData: false,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      { target: 'data', props: ['amount'] },
    ],
  },
);

export const useFinancialsGetAdvancePaymentBe = createDCHook(
  'getAdvancePaymentBe',
  collapseDataFromCall(getAdvancePaymentBe),
  { injectables: ['label', 'customerId', 'accountId'], flattenData: true },
  { maxAge: 15000 },
);

export const useFinancialsPutDesiredAdvancePayment = createMutationHook(
  collapseParams(collapseParams(putDesiredAdvancePayment, 'requestBody', 'data'), 'data', 'amount'),
  {
    injectables: ['label', 'customerId', 'businessUnit'],
    flattenData: false,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      { target: 'data', props: ['amount'] },
    ],
  },
);

export const useFinancialsGetFinancialOverview = createDCHook(
  'getFinancialOverview',
  collapseDataFromCall(getFinancialOverview),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
);

export const useFinancialsGetFinancialPreferences = createDCHook(
  'getFinancialPreferences',
  collapseDataFromCall(getFinancialPreferences),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
  { maxAge: 15000 },
);

export const useFinancialsUpdateFinancialPreferences = createMutationHook(
  collapseParams(
    collapseParams(updateFinancialPreferences, 'requestBody', 'data'),
    'data',
    'paymentMethodIsDirectDebit',
    'paymentDayOfMonth',
    'bankAccount',
    'actionFeedbackData',
  ),
  {
    injectables: ['label', 'customerId', 'accountId', 'businessUnit'],
    flattenData: false,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      {
        target: 'data',
        props: ['paymentMethodIsDirectDebit', 'paymentDayOfMonth', 'bankAccount', 'actionFeedbackData'],
      },
    ],
  },
);

export const useFinancialsDownloadInvoiceDocument = createDCHook('downloadInvoiceDocument', downloadInvoiceDocument, {
  injectables: ['label', 'customerId', 'accountId', 'businessUnit'],
  flattenData: false,
});

export const useFinancialsGetPaymentArrangement = createDCHook(
  'getPaymentArrangement',
  collapseDataFromCall(getPaymentArrangement),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
);

export const useFinancialsCreatePaymentArrangement = createMutationHook(
  collapseDataFromCall(
    collapseParams(
      collapseParams(createPaymentArrangement, 'requestBody', 'data'),
      'data',
      'termsamount',
      'expirationdatefirstterm',
      'excludedinvoices',
    ),
  ),
  {
    injectables: ['label', 'customerId', 'accountId', 'businessUnit'],
    flattenData: true,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      { target: 'data', props: ['termsamount', 'expirationdatefirstterm', 'excludedinvoices'] },
    ],
  },
);

export const useFinancialsProposePaymentArrangement = createMutationHook(
  collapseDataFromCall(
    collapseParams(
      collapseParams(proposePaymentArrangement, 'requestBody', 'data'),
      'data',
      'termsamount',
      'expirationdatefirstterm',
      'excludedinvoices',
    ),
  ),
  {
    injectables: ['label', 'customerId', 'accountId', 'businessUnit'],
    flattenData: true,
    flattenBodyPairs: [
      { target: 'requestBody', props: ['data'] },
      { target: 'data', props: ['termsamount', 'expirationdatefirstterm', 'excludedinvoices'] },
    ],
  },
);

export const useFinancialsGetAdvancePaymentAdvice = createDCHook(
  'getAdvancePaymentAdvice',
  collapseDataFromCall(getAdvancePaymentAdvice),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
);

export const useFinancialsGetAdvancePaymentAdviceV2 = createDCHook(
  'getAdvancePaymentAdviceV2',
  collapseDataFromCall(getAdvancePaymentAdviceV2),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
  { maxAge: 15000 },
);

export const useFinancialsGetInvoicesOverview = createDCHook(
  'getInvoicesOverview',
  collapseDataFromCall(getInvoicesOverview),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
);

export const useFinancialsGetPaymentPlan = createDCHook('getPaymentPlan', collapseDataFromCall(getPaymentPlan), {
  injectables: ['label', 'customerId', 'accountId', 'businessUnit'],
  flattenData: true,
});

export const useFinancialsGetPaymentPlanBreakdown = createDCHook(
  'getPaymentPlanBreakdown',
  collapseDataFromCall(getPaymentPlanBreakdown),
  { injectables: ['label', 'customerId', 'accountId', 'businessUnit'], flattenData: true },
);
