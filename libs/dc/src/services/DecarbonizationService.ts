import { DC_Products_Client_Models_BuildingType } from '@monorepo-types/dc';
import { DC_Products_Client_Models_ConstructionPeriod } from '@monorepo-types/dc';
import { DC_Products_Client_Models_CurrentHeatingSystem } from '@monorepo-types/dc';
import { DC_Products_Client_Models_FloorAreaM2 } from '@monorepo-types/dc';
import { DC_Repositories_Base_Enumerations_BusinessUnit } from '@monorepo-types/dc';
import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';
import { ResponseDataListDC_Products_Client_Models_DecarbPotential } from '@monorepo-types/dc';

import { request } from '../client';
import type { ApiRequestConfig } from '../client/types';

type GetDecarbonizationPotentials = {
  businessUnit: DC_Repositories_Base_Enumerations_BusinessUnit;
  label: DC_Repositories_Base_Enumerations_Label;
  buildingType: DC_Products_Client_Models_BuildingType;
  floorAreaM2: DC_Products_Client_Models_FloorAreaM2;
  numberOfFloors: number;
  constructionPeriod: DC_Products_Client_Models_ConstructionPeriod;
  currentHeatingSystem: DC_Products_Client_Models_CurrentHeatingSystem;
};
/**
 * GetDecarbonizationPotentials
 * Get the customer profile including any open orders
 * @returns ResponseDataListDC_Products_Client_Models_DecarbPotential Success
 */
export function getDecarbonizationPotentials(
  {
    businessUnit,
    label,
    buildingType,
    floorAreaM2,
    numberOfFloors,
    constructionPeriod,
    currentHeatingSystem,
  }: GetDecarbonizationPotentials,
  requestConfig: ApiRequestConfig = {},
): Promise<ResponseDataListDC_Products_Client_Models_DecarbPotential> {
  return request(
    {
      method: 'GET',
      path: `/dxpweb/${businessUnit}/${label}/decarbonization/potentials`,
      query: { buildingType, floorAreaM2, numberOfFloors, constructionPeriod, currentHeatingSystem },
      errors: { 400: 'Bad Request' },
    },
    requestConfig,
  );
}
