/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/MyZone/Consumption/ConsumptionGraphicalOverview
 */
export interface ConsumptionGraphicalOverviewRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  data: Data;
  meterReadingsTable: MeterReadingsTable;
}
export interface Data {
  addressFilterLabel: TextField;
  consumptionTypeLabel: TextField;
  consumptionTypeOptionsList: NameLabelListField;
  description: TextField;
  disclaimerInfoDescription: TextField;
  disclaimerText: TextField;
  graphTitle: TextField;
  periodLabel: TextField;
  periodOptionsList: NameLabelListField;
  title: TextField;
  legendTitle: TextField;
  legendTotalLabel: TextField;
  legendElectricityLabel: TextField;
  legendGasLabel: TextField;
  legendInjectionLabel: TextField;
  legendInjectionDescription: TextField;
  legendFixedCostLabel: TextField;
  legendFixedCostDescription: TextField;
  legendFixedCostInfoDescription: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface NameLabelListField {
  editable?: string;
  value: {
    enum: {
      label: string;
      name: string;
      value: string;
    }[];
  };
}
export interface MeterReadingsTable {
  addMeterReadingButtonText: TextField;
  dateTableHeaderText: TextField;
  deleteText: TextField;
  deleteValueText: TextField;
  energyOptionsList: NameLabelListField;
  meterReadingTableText: TextField;
  meterReadingTableTitle: TextField;
  originInformativeText: TextField;
  originMoreInfoPopoverContent: TextField;
  originMoreInfoText: TextField;
  originOfficialText: TextField;
  originText: TextField;
  originUserText: TextField;
  unableToDeleteValueText: TextField;
}
