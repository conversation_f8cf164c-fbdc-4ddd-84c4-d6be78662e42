/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/Flows/EnergyFlow/OverviewStep
 */
export interface OverviewStepRendering {
  fields: Fields;
  uid: string;
  componentName: string;
  dataSource: string;
  params: {};
  datasourceRequired: boolean;
}
export interface Fields {
  genericError: GenericError;
  notifications: Notifications;
  content: Content;
  productTypeLabels: ProductTypeLabels;
  footer: Footer;
  promotionTexts: PromotionTexts;
  giftData: GiftData;
  costDetailsExplanation: CostDetailsExplanation;
  costDetails: CostDetails;
  electricityCostDetails: ElectricityCostDetails;
  gasCostDetails: GasCostDetails;
  warmthCostDetails: WarmthCostDetails;
}
export interface GenericError {
  errorNotAvailableNotification: NotificationField;
  errorNotification: NotificationField;
  errorNotFoundNotification: NotificationField;
  tryAgainButtonText: TextField;
}
export interface GiftData {
  giftExplanationTriggerText: TextField;
  giftProductDataList: FlowsNewEnergy;
}
export type FlowsNewEnergy = {
  id: string;
  url: string;
  name: string;
  displayName: string;
  fields: {
    content?: Content;
    gift?: Gift;
  };
  [k: string]: unknown;
}[];
export interface Gift {
  giftId: TextField;
}

export interface NotificationField {
  value: {
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
    title: string;
    content: string;
  };
  editable?: string;
}
export interface TextField {
  value: string;
  editable?: string;
}
export interface Notifications {
  dynamicPricingContractNotification: NotificationField;
  hybridContractNotification: NotificationField;
}

export interface NameLabelListField {
  editable?: string;
  value: {
    enum: {
      label: string;
      name: string;
      value: string;
    }[];
  };
}
export interface Content {
  moreInfoDialog: DialogField;
  notification: NotificationField;
  usp2Content: TextField;
  usp3Content: TextField;
  title: TextField;
  totalPriceLabel: TextField;
  promotionPriceSingleLabel: TextField;
  text: TextField;
  priceIncludingText: TextField;
  promotionPricePluralLabel: TextField;
  usp1Content: TextField;
  nextStepText: TextField;
  priceDetailsDialog: DialogField;
  descriptionContent: TextField;
  timeOfUseInfoDialog: DialogField;
  timeOfUseInfoDialogUspList: NameLabelListField;
}
export interface DialogField {
  value: {
    title: string;
    content: string;
    triggerText: string;
    submitButtonText: string | null;
    cancelButtonText: string | null;
  };
  editable?: string;
}
export interface ProductTypeLabels {
  electricityProductLabel: TextField;
  geothermalHeatingProductLabel: TextField;
  gasProductLabel: TextField;
  warmthProductLabel: TextField;
  waterProductLabel: TextField;
  warmthExchangeSetLabel: TextField;
  cashbackDirectLabel: TextField;
  cashbackOnYearNoteLabel: TextField;
}
export interface Footer {
  gasLabel: TextField;
  modifyTriggerText: TextField;
  electricityHighLabel: TextField;
  electricityLabel: TextField;
  waterLabel: TextField;
  footerDescription: TextField;
  electricityLowLabel: TextField;
  redeliveryLabel: TextField;
  warmthLabel: TextField;
}
export interface PromotionTexts {
  cashbackOnYearNoteRibbonContent: TextField;
  cashbackDirectRibbonContent: TextField;
  cashbackOnYearNoteLabelContent: TextField;
  cashbackDirectLabelContent: TextField;
  cashbackExplanationTriggerText: TextField;
  cashbackDirectExplanationContent: TextField;
  cashbackOnYearNoteExplanationContent: TextField;
  timeOfUseRibbonContent: TextField;
}
export interface CostDetailsExplanation {
  contractEndDateParagraph: ParagraphField;
  currentRatesParagraph: ParagraphField;
  explanationTitle: TextField;
  governmentAndLeviesParagraph: ParagraphField;
  monthlyFeesParagraph: ParagraphField;
  priceDifferencesParagraph: ParagraphField;
  residenceFunctionParagraph: ParagraphField;
  fixedCostsParagraph: ParagraphField;
  governmentLeviesParagraph: ParagraphField;
  monthlyFeesDynamicPricingProductParagraph: ParagraphField;
  monthlyFeesVariablePricingProductParagraph?: ParagraphField;
  monthlyFeesHybridParagraph: ParagraphField;
  deliveryCostsParagraph: ParagraphField;
  variableCostsParagraph: ParagraphField;
  redeliveryDynamicPricingParagraph: ParagraphField;
  redeliveryHybridParagraph: ParagraphField;
  redeliveryParagraph: ParagraphField;
  lowHighTariffDifferenceParagraph: ParagraphField;
  timeOfUseLowHighTariffDifferenceParagraph: ParagraphField;
}
export interface ParagraphField {
  value: {
    title: string;
    content: string;
  };
  editable?: string;
}
export interface CostDetails {
  totalCostsFirstYearLabel: TextField;
  title: TextField;
  fixedCostsLabel: TextField;
  totalCostsLabel: TextField;
  variableCostsLabel: TextField;
  text: TextField;
  perMonthLabel: TextField;
  perYearLabel: TextField;
  dynamicPricingNotification: NotificationField;
  hybridNotification: NotificationField;
  variableProductNotification: NotificationField;
  usageLabel: TextField;
  monthlyTermLabel: TextField;
  yearlyTermLabel: TextField;
}
export interface ElectricityCostDetails {
  redeliveryTariffLabel: TextField;
  redeliveryCostLabel: TextField;
  redeliveryLabel: TextField;
  tariffDeliveryCostsLabel: TextField;
  redeliveryDynamicPricingProductTariffLabel: TextField;
  tariffDynamicPricingProductDeliveryCostsLabel: TextField;
  lowTarifflLabel: TextField;
  timeOfUseLowTariffLabel: TextField;
  redeliveryLessNotification: NotificationField;
  standingChargeLabel: TextField;
  energyTaxLabel: TextField;
  deliveryCostsLabel: TextField;
  storageSustainableEnergyLabel: TextField;
  tariffLabel: TextField;
  timeOfUseTariffLabel: TextField;
  redeliveryMoreNotification: NotificationField;
  taxDiscountLabel: TextField;
  governmentLeviesLabel: TextField;
  taxBracket1Label: TextField;
  taxBracket2Label: TextField;
  taxBracket3Label: TextField;
  redeliveryCompensationEndDateText: TextField;
  redeliveryCompensationRateDialog: DialogField;
  redeliveryCompensationRateText: TextField;
  redeliveryCostsEndDateText: TextField;
  redeliveryCostsRateDialog: DialogField;
  redeliveryCostsRateText: TextField;
}
export interface GasCostDetails {
  standingChargeLabel: TextField;
  storageSustainableEnergyLabel: TextField;
  tariffLabel: TextField;
  deliveryCostsLabel: TextField;
  energyTaxLabel: TextField;
  governmentLeviesLabel: TextField;
  taxBracket1Label: TextField;
  taxBracket2Label: TextField;
}
export interface WarmthCostDetails {
  deliveryCostsLabel: TextField;
  standingChargeLabel: TextField;
  variableCostsWarmthLabel: TextField;
  variableCostsWaterLabel: TextField;
  tariffColdWaterLabel: TextField;
  tariffLabel: TextField;
  tariffWarmWaterLabel: TextField;
  taxColdWaterLabel: TextField;
  rentWarmthSet: TextField;
  standingChargeGeothermalHeatingLabel: TextField;
  geothermalHeatingPompCostsLabel: TextField;
  variableCostsGeothermalHeatingLabel: TextField;
}
