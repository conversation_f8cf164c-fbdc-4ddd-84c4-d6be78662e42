/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

import { FocalPointImage } from './lib';

/**
 * /sitecore/layout/Renderings/Feature/Flows/PaymentArrangementFlow/PaymentArrangementEndStep
 */
export interface PaymentArrangementEndStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  image: FocalPointImage;
  card: {
    title: TextField;
    content: TextField;
    cta: GeneralLinkField;
  };
}

export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
