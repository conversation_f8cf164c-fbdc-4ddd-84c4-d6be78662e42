/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

export type FlowsNewEnergyOfferStep = {
  id: string;
  url: string;
  name: string;
  displayName: string;
  fields: {
    data?: Data;
    unitPriceLabels?: {
      electricityHighLabel?: TextField;
      electricityLabel?: TextField;
      electricityLowLabel?: TextField;
      gasLabel?: TextField;
      redeliveryLabel?: TextField;
      warmthLabel?: TextField;
      waterColdLabel?: TextField;
      waterWarmLabel?: TextField;
    };
  };
  [k: string]: unknown;
}[];

/**
 * /sitecore/layout/Renderings/Feature/Flows/EnergyFlow/OfferStep
 */
export interface OfferStepRendering {
  fields: Fields;
  uid: string;
  componentName: string;
  dataSource: string;
  params: {
    allowDynamicOrHybridOnly: string;
  };
  datasourceRequired: boolean;
}
export interface Fields {
  productData: ProductData;
  genericError: GenericError;
  content: Content;
  costDetails: CostDetails;
  costDetailsExplanation: CostDetailsExplanation;
  electricityCostDetails: ElectricityCostDetails;
  gasCostDetails: GasCostDetails;
  warmthCostDetails: WarmthCostDetails;
  footer: Footer;
  promotionTexts: PromotionTexts;
  productTypeLabels: ProductTypeLabels;
  unitPriceLabels: UnitPriceLabels;
  giftData: GiftData;
  dynamicOffer: DynamicOffer;
}

export interface DynamicOffer {
  noDynamicOfferAvailableTitle: TextField;
  noDynamicOfferAvailableText: TextField;
  noDynamicOfferAvailableDialog: DialogField;
}

export interface Choice {
  typeOfContractRadio: RadioGroupField;
}

export interface NameLabelListField {
  editable?: string;
  value: {
    enum: {
      label: string;
      name: string;
      value: string;
    }[];
  };
}

export interface RadioGroupField {
  value: {
    options: {
      name: string;
      label: string;
      description?: string;
    }[];
    label: string;
    hint: string;
    requiredMessage: string;
  };
  editable?: string;
}
export interface ProductData {
  contractTypeRadio: RadioGroupField;
  perMonthEstimatedLabel: TextField;
  variablePeriodLabel: TextField;
  tariffsTriggerText: TextField;
  yearLabel: TextField;
  monthsLabel: TextField;
  perMonthLabel: TextField;
  productDataList: FlowsNewEnergyOfferStep;
  fixedProductNameText: TextField;
  variableProductNameText: TextField;
  hybridProductNameText: TextField;
  dynamicProductNameText: TextField;
  variableProductNotification: NotificationField;
}
export interface TextField {
  value: string;
  editable?: string;
}
export interface Data {
  byPeriodLabel: TextField;
  id: TextField;
  usp2Text: TextField;
  usp1Text: TextField;
  usp3Text: TextField;
  usp4Text: TextField;
  name: TextField;
  promotionText: TextField;
  tariffDescriptionText: TextField;
  tariffSplitExplanationText: TextField;
}
export interface GenericError {
  errorNotAvailableNotification: NotificationField;
  errorNotAuthenticatedNotification: NotificationField;
  loginButtonText: TextField;
  errorNotFoundNotification: NotificationField;
  tryAgainButtonText: TextField;
  errorNotification: NotificationField;
}
export interface NotificationField {
  value: {
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
    title: string;
    content: string;
  };
  editable?: string;
}
export interface Content {
  moreInfoDialog: DialogField;
  notification: NotificationField;
  nextStepText: TextField;
  text: TextField;
  title: TextField;
  contractTypeTitle?: TextField;
  descriptionContent: TextField;
  noOfferSelectedNotification?: NotificationField;
  timeOfUseInfoDialog: DialogField;
  timeOfUseInfoDialogUspList: NameLabelListField;
}
export interface DialogField {
  value: {
    title: string;
    content: string;
    triggerText: string;
    submitButtonText: string | null;
    cancelButtonText: string | null;
  };
  editable?: string;
}
export interface Footer {
  gasLabel: TextField;
  modifyTriggerText: TextField;
  electricityHighLabel: TextField;
  electricityLabel: TextField;
  waterLabel: TextField;
  footerDescription: TextField;
  electricityLowLabel: TextField;
  redeliveryLabel: TextField;
  warmthLabel: TextField;
}
export interface PromotionTexts {
  cashbackOnYearNoteRibbonContent: TextField;
  cashbackDirectRibbonContent: TextField;
  cashbackOnYearNoteLabelContent: TextField;
  cashbackDirectLabelContent: TextField;
  cashbackExplanationTriggerText: TextField;
  cashbackDirectExplanationContent: TextField;
  cashbackOnYearNoteExplanationContent: TextField;
  timeOfUseRibbonContent: TextField;
}
export interface UnitPriceLabels {
  electricityLabel: TextField;
  warmthLabel: TextField;
  waterColdLabel: TextField;
  electricityHighLabel: TextField;
  electricityLowLabel: TextField;
  waterWarmLabel: TextField;
  gasLabel: TextField;
  redeliveryLabel: TextField;
}

export interface CostDetails {
  totalCostsFirstYearLabel: TextField;
  title: TextField;
  fixedCostsLabel: TextField;
  totalCostsLabel: TextField;
  variableCostsLabel: TextField;
  text: TextField;
  perMonthLabel: TextField;
  perYearLabel: TextField;
  dynamicPricingNotification: NotificationField;
  hybridNotification: NotificationField;
  variableProductNotification: NotificationField;
  usageLabel: TextField;
  monthlyTermLabel: TextField;
  yearlyTermLabel: TextField;
}

export interface CostDetailsExplanation {
  contractEndDateParagraph: ParagraphField;
  currentRatesParagraph: ParagraphField;
  explanationTitle: TextField;
  governmentAndLeviesParagraph: ParagraphField;
  monthlyFeesParagraph: ParagraphField;
  priceDifferencesParagraph: ParagraphField;
  residenceFunctionParagraph: ParagraphField;
  fixedCostsParagraph: ParagraphField;
  governmentLeviesParagraph: ParagraphField;
  monthlyFeesDynamicPricingProductParagraph: ParagraphField;
  monthlyFeesVariablePricingProductParagraph?: ParagraphField;
  monthlyFeesHybridParagraph: ParagraphField;
  deliveryCostsParagraph: ParagraphField;
  variableCostsParagraph: ParagraphField;
  redeliveryDynamicPricingParagraph: ParagraphField;
  redeliveryHybridParagraph: ParagraphField;
  redeliveryParagraph: ParagraphField;
  lowHighTariffDifferenceParagraph: ParagraphField;
  timeOfUseLowHighTariffDifferenceParagraph: ParagraphField;
}

export interface ElectricityCostDetails {
  redeliveryTariffLabel: TextField;
  redeliveryCostLabel: TextField;
  redeliveryLabel: TextField;
  tariffDeliveryCostsLabel: TextField;
  redeliveryCompensationEndDateText: TextField;
  redeliveryCompensationRateText: TextField;
  redeliveryCostsEndDateText: TextField;
  redeliveryCostsRateText: TextField;
  redeliveryDynamicPricingProductTariffLabel: TextField;
  tariffDynamicPricingProductDeliveryCostsLabel: TextField;
  lowTarifflLabel: TextField;
  timeOfUseLowTariffLabel: TextField;
  redeliveryLessNotification: NotificationField;
  standingChargeLabel: TextField;
  energyTaxLabel: TextField;
  deliveryCostsLabel: TextField;
  storageSustainableEnergyLabel: TextField;
  tariffLabel: TextField;
  timeOfUseTariffLabel: TextField;
  redeliveryMoreNotification: NotificationField;
  taxDiscountLabel: TextField;
  governmentLeviesLabel: TextField;
  taxBracket1Label: TextField;
  taxBracket2Label: TextField;
  taxBracket3Label: TextField;
}

export interface ProductTypeLabels {
  electricityProductLabel: TextField;
  geothermalHeatingProductLabel: TextField;
  gasProductLabel: TextField;
  warmthProductLabel: TextField;
  waterProductLabel: TextField;
  warmthExchangeSetLabel: TextField;
  cashbackDirectLabel: TextField;
  cashbackOnYearNoteLabel: TextField;
}

export interface GasCostDetails {
  standingChargeLabel: TextField;
  storageSustainableEnergyLabel: TextField;
  tariffLabel: TextField;
  deliveryCostsLabel: TextField;
  energyTaxLabel: TextField;
  governmentLeviesLabel: TextField;
  taxBracket1Label: TextField;
  taxBracket2Label: TextField;
}
export interface WarmthCostDetails {
  deliveryCostsLabel: TextField;
  standingChargeLabel: TextField;
  variableCostsWarmthLabel: TextField;
  variableCostsWaterLabel: TextField;
  tariffColdWaterLabel: TextField;
  tariffLabel: TextField;
  tariffWarmWaterLabel: TextField;
  taxColdWaterLabel: TextField;
  rentWarmthSet: TextField;
  standingChargeGeothermalHeatingLabel: TextField;
  geothermalHeatingPompCostsLabel: TextField;
  variableCostsGeothermalHeatingLabel: TextField;
}
export interface GiftData {
  giftExplanationTriggerText: TextField;
  giftProductDataList: FlowsNewEnergy;
}
export type FlowsNewEnergy = {
  id: string;
  url: string;
  name: string;
  displayName: string;
  fields: {
    content?: Content;
    gift?: Gift;
  };
  [k: string]: unknown;
}[];
export interface Gift {
  giftId: TextField;
}
