/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/Flows/EnergyFlow/SummaryStep
 */
export interface SummaryStepRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  content: Content;
  costDetails: CostDetails;
  costDetailsExplanation: CostDetailsExplanation;
  electricityCostDetails: ElectricityCostDetails;
  gasCostDetails: GasCostDetails;
  genericError: GenericError;
  notifications: Notifications;
  paymentDetails: PaymentDetails;
  personalDetailsOverview: PersonalDetailsOverview;
  productsOverview: ProductsOverview;
  productTypeLabels: ProductTypeLabels;
  promotionTexts: PromotionTexts;
  salesConditions: SalesConditions;
  warmthCostDetails: WarmthCostDetails;
}
export interface Content {
  nextStepText: TextField;
  notification: NotificationField;
  priceIncludingText: TextField;
  text: TextField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface NotificationField {
  editable?: string;
  value: {
    content: string;
    title: string;
    variant: 'info' | 'success' | 'warning' | 'error' | 'chat';
  };
}
export interface CostDetails {
  dynamicPricingNotification: NotificationField;
  fixedCostsLabel: TextField;
  hybridNotification: NotificationField;
  monthlyTermLabel: TextField;
  perMonthLabel: TextField;
  perYearLabel: TextField;
  text: TextField;
  title: TextField;
  totalCostsFirstYearLabel: TextField;
  totalCostsLabel: TextField;
  usageLabel: TextField;
  variableCostsLabel: TextField;
  variableProductNotification: NotificationField;
  yearlyTermLabel: TextField;
}
export interface CostDetailsExplanation {
  contractEndDateParagraph: ParagraphField;
  currentRatesParagraph: ParagraphField;
  deliveryCostsParagraph: ParagraphField;
  explanationTitle: TextField;
  fixedCostsParagraph: ParagraphField;
  governmentAndLeviesParagraph: ParagraphField;
  governmentLeviesParagraph: ParagraphField;
  lowHighTariffDifferenceParagraph: ParagraphField;
  timeOfUseLowHighTariffDifferenceParagraph: ParagraphField;
  monthlyFeesDynamicPricingProductParagraph: ParagraphField;
  monthlyFeesVariablePricingProductParagraph?: ParagraphField;
  monthlyFeesHybridParagraph: ParagraphField;
  monthlyFeesParagraph: ParagraphField;
  priceDifferencesParagraph: ParagraphField;
  redeliveryDynamicPricingParagraph: ParagraphField;
  redeliveryHybridParagraph: ParagraphField;
  redeliveryParagraph: ParagraphField;
  residenceFunctionParagraph: ParagraphField;
  variableCostsParagraph: ParagraphField;
}
export interface ParagraphField {
  editable?: string;
  value: {
    content: string;
    title: string;
  };
}
export interface ElectricityCostDetails {
  deliveryCostsLabel: TextField;
  energyTaxLabel: TextField;
  governmentLeviesLabel: TextField;
  lowTarifflLabel: TextField;
  timeOfUseLowTariffLabel: TextField;
  redeliveryCompensationEndDateText: TextField;
  redeliveryCompensationRateDialog: DialogField;
  redeliveryCompensationRateText: TextField;
  redeliveryCostLabel: TextField;
  redeliveryCostsEndDateText: TextField;
  redeliveryCostsRateDialog: DialogField;
  redeliveryCostsRateText: TextField;
  redeliveryDynamicPricingProductTariffLabel: TextField;
  redeliveryLabel: TextField;
  redeliveryLessNotification: NotificationField;
  redeliveryMoreNotification: NotificationField;
  redeliveryTariffLabel: TextField;
  standingChargeLabel: TextField;
  storageSustainableEnergyLabel: TextField;
  tariffDeliveryCostsLabel: TextField;
  tariffDynamicPricingProductDeliveryCostsLabel: TextField;
  tariffLabel: TextField;
  timeOfUseTariffLabel: TextField;
  taxBracket1Label: TextField;
  taxBracket2Label: TextField;
  taxBracket3Label: TextField;
  taxDiscountLabel: TextField;
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
export interface GasCostDetails {
  deliveryCostsLabel: TextField;
  energyTaxLabel: TextField;
  governmentLeviesLabel: TextField;
  standingChargeLabel: TextField;
  storageSustainableEnergyLabel: TextField;
  tariffLabel: TextField;
  taxBracket1Label: TextField;
  taxBracket2Label: TextField;
}
export interface GenericError {
  errorNotAuthenticatedNotification: NotificationField;
  errorNotAvailableNotification: NotificationField;
  errorNotFoundNotification: NotificationField;
  fetchOfferErrorNotification: NotificationField;
  loginButtonText: TextField;
  sendOrderErrorNotification: NotificationField;
  tryAgainButtonText: TextField;
}
export interface Notifications {
  dynamicPricingContractNotification: NotificationField;
  hybridContractNotification: NotificationField;
}
export interface PaymentDetails {
  description: TextField;
  ibanDialog: DialogField;
  ibanFormField: CustomFormField;
  title: TextField;
}
export interface CustomFormField {
  editable?: string;
  value: {
    hint: string;
    label: string;
    placeholder: string;
    requiredMessage: string;
    validationMessage: string;
  };
}
export interface PersonalDetailsOverview {
  addressDetailsLabel: TextField;
  birthDateLabel: TextField;
  contactDetailsLabel: TextField;
  editDetailsLabel: TextField;
  emailAddressLabel: TextField;
  nameLabel: TextField;
  personalDetailsLabel: TextField;
  phoneNumberLabel: TextField;
  situationDetailsLabel: TextField;
  title: TextField;
  willBeMovingLabel: TextField;
  wontBeMovingLabel: TextField;
}
export interface ProductsOverview {
  contractEndDateLabel: TextField;
  contractStartDateLabel: TextField;
  costDetailsDialog: DialogField;
  title: TextField;
  totalFirstYearLabel: TextField;
}
export interface ProductTypeLabels {
  cashbackDirectLabel: TextField;
  cashbackOnYearNoteLabel: TextField;
  electricityProductLabel: TextField;
  gasProductLabel: TextField;
  geothermalHeatingProductLabel: TextField;
  warmthExchangeSetLabel: TextField;
  warmthProductLabel: TextField;
  waterProductLabel: TextField;
}
export interface PromotionTexts {
  cashbackDirectExplanationContent: TextField;
  cashbackDirectLabelContent: TextField;
  cashbackDirectRibbonContent: TextField;
  cashbackExplanationTriggerText: TextField;
  cashbackOnYearNoteExplanationContent: TextField;
  cashbackOnYearNoteLabelContent: TextField;
  cashbackOnYearNoteRibbonContent: TextField;
  timeOfUseRibbonContent: TextField;
}
export interface SalesConditions {
  conditionsParagraph: ParagraphField;
  considerationPeriodParagraph: ParagraphField;
  content: TextField;
  extensionParagraph: ParagraphField;
  noticePeriodDynamicPricingProductParagraph: ParagraphField;
  noticePeriodHybridParagraph: ParagraphField;
  noticePeriodParagraph: ParagraphField;
  objectionParagraph: ParagraphField;
  salesConditionsDialog: DialogField;
  tariffsDialog: DialogField;
  title: TextField;
  variablePricingHybridParagraph: ParagraphField;
  variablePricingParagraph: ParagraphField;
}
export interface WarmthCostDetails {
  deliveryCostsLabel: TextField;
  geothermalHeatingPompCostsLabel: TextField;
  rentWarmthSet: TextField;
  standingChargeGeothermalHeatingLabel: TextField;
  standingChargeLabel: TextField;
  tariffColdWaterLabel: TextField;
  tariffLabel: TextField;
  tariffWarmWaterLabel: TextField;
  taxColdWaterLabel: TextField;
  variableCostsGeothermalHeatingLabel: TextField;
  variableCostsWarmthLabel: TextField;
  variableCostsWaterLabel: TextField;
}
