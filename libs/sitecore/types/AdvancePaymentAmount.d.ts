/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/SelfService/AdvancePaymentAmount
 */
export interface AdvancePaymentAmountRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  advancePaymentBucketTitle: TextField;
  advancePaymentBuildUpContent: TextField;
  advancePaymentBuildUpModalContent: TextField;
  advancePaymentBuildUpModalTitle: TextField;
  advancePaymentBuildUpNoDataFoundContent: TextField;
  advancePaymentBuildUpTitle: TextField;
  advancePaymentOpenModalButtonText: TextField;
  adviceBucketEditLink: GeneralLinkField;
  adviceBuildUpDialog: DialogField;
  adviceBuildUpGraphAmountLabel: TextField;
  adviceBuildUpGraphCostsLabel: TextField;
  adviceBuildUpGraphFootnoteDescription: TextField;
  adviceCurrentTitle: TextField;
  adviceDecreaseCta: TextField;
  adviceDecreaseDescription: TextField;
  adviceIncreaseCta: TextField;
  adviceIncreaseDescription: TextField;
  adviceRecommendedTitle: TextField;
  adviceTakeOverErrorDesciption: TextField;
  adviceTakeOverErrorText: TextField;
  adviceTakeOverEstimatedMeterReadingsErrorText: TextField;
  adviceTakeOverMultipleAccountsErrorText: TextField;
  adviceTooHighDescription: TextField;
  adviceTooHighTitle: TextField;
  adviceTooLowDescription: TextField;
  adviceTooLowTitle: TextField;
  cannotUpdateDescription: TextField;
  cannotUpdateTitle: TextField;
  content: TextField;
  editLink: GeneralLinkField;
  forecastHigherText: TextField;
  forecastHigherTitle: TextField;
  forecastLowerText: TextField;
  forecastLowerTitle: TextField;
  formFeedbackErrorTitle: TextField;
  hasRedeliveryDescription: TextField;
  hasRedeliveryTitle: TextField;
  hasWarmthDescription: TextField;
  hasWarmthTitle: TextField;
  meterInErrorDescription: TextField;
  meterInErrorTitle: TextField;
  newCustomerDescription: TextField;
  newCustomerTitle: TextField;
  noActiveMandateDescription: TextField;
  noActiveMandateTitle: TextField;
  noAdviceYetDescription: TextField;
  noAdviceYetTitle: TextField;
  notAvailableDescription: TextField;
  notAvailableTitle: TextField;
  notImplementedDescription: TextField;
  notImplementedTitle: TextField;
  paymentAdviceTitle: TextField;
  paymentAmountHigherDescription: TextField;
  paymentAmountHigherTitle: TextField;
  paymentAmountLowerDescription: TextField;
  paymentAmountLowerTitle: TextField;
  paymentIsFineDescription: TextField;
  paymentIsFineTitle: TextField;
  paymentOkTitle: TextField;
  periodBlockText: TextField;
  periodBlockTitle: TextField;
  tableAmountLabel: TextField;
  tableAmountPassedLabel: TextField;
  tableAmountRemainingLabel: TextField;
  tableCostsLabel: TextField;
  tableCostsPassedLabel: TextField;
  tableCostsRemainingLabel: TextField;
  tableDifferenceDuePayLabel: TextField;
  tableDifferenceDueReturnLabel: TextField;
  tableFootnoteDescription: TextField;
  tableTillMonthAndIncludingLabel: TextField;
  tableTitle: TextField;
  title: TextField;
  tooLowToUpdateDescription: TextField;
  tooLowToUpdateTitle: TextField;
  updateAdviceDescription: TextField;
  updateAdviceMixedMetersDescription: TextField;
  updateAdviceMixedMetersTitle: TextField;
  updateAdviceTitle: TextField;
  yearNoteTooCloseDescription: TextField;
  yearNoteTooCloseTitle: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
