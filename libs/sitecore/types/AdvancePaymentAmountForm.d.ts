/**
 * This file is auto-generated by the 'generate-types-sitecore' script.
 * Do not manually edit; your changes might get lost
 */

/**
 * /sitecore/layout/Renderings/Feature/SelfService/AdvancePaymentAmountForm
 */
export interface AdvancePaymentAmountFormRendering {
  componentName: string;
  dataSource: string;
  datasourceRequired: boolean;
  fields: Fields;
  params: {};
  uid: string;
}
export interface Fields {
  advancePaymentAmountLabel: TextField;
  cancelLink: GeneralLinkField;
  cannotUpdateBackLink: GeneralLinkField;
  cannotUpdateBackText: TextField;
  cannotUpdateMaximumDescription: TextField;
  cannotUpdateMaximumTitle: TextField;
  cannotUpdateMinimumDescription: TextField;
  cannotUpdateMinimumRangeExplanationDialog: DialogField;
  cannotUpdateMinimumTitle: TextField;
  cannotUpdateTitle: TextField;
  cannotUpdateTooHighErrorDescription: TextField;
  cannotUpdateTooHighErrorTitle: TextField;
  cannotUpdateTooLowErrorDescription: TextField;
  cannotUpdateTooLowErrorTitle: TextField;
  content: TextField;
  currentAmountText: TextField;
  formFeedbackDefaultErrorDescription: TextField;
  formFeedbackEstimatedMeterReadingsErrorDescription: TextField;
  formFeedbackMultipleAccountsErrorDescription: TextField;
  formFeedbackSuccessText: TextField;
  increaseWillBePermanentDescription: TextField;
  increaseWillBePermanentTitle: TextField;
  noAmountFieldErrorText: TextField;
  noAmountTitle: TextField;
  saveButtonText: TextField;
  selfserviceCustomerServiceLink: GeneralLinkField;
  selfserviceInvoicesLink: GeneralLinkField;
  title: TextField;
}
export interface TextField {
  editable?: string;
  value: string;
}
export interface GeneralLinkField {
  editable?: string;
  value: {
    anchor: string;
    class: string;
    href: string;
    id: string;
    linktype: string;
    querystring: string;
    target: string;
    text: string;
    title: string;
    url: string;
  };
}
export interface DialogField {
  editable?: string;
  value: {
    cancelButtonText: string | null;
    content: string;
    submitButtonText: string | null;
    title: string;
    triggerText: string;
  };
}
