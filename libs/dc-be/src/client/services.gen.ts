// This file is auto-generated by @hey-api/openapi-ts

import { createClient, createConfig, type Options, formDataBodySerializer } from '@hey-api/client-fetch';
import type { GetEnecoBeXapiSiteApiV1ProfileError, GetEnecoBeXapiSiteApiV1ProfileResponse, PutEnecoBeXapiSiteApiV1ProfileLocalData, PutEnecoBeXapiSiteApiV1ProfileLocalError, PutEnecoBeXapiSiteApiV1ProfileLocalResponse, PostEnecoBeXapiSiteApiV1ProfileItsmeData, PostEnecoBeXapiSiteApiV1ProfileItsmeError, PostEnecoBeXapiSiteApiV1ProfileItsmeResponse, DeleteEnecoBeXapiSiteApiV1ProfileItsmeError, DeleteEnecoBeXapiSiteApiV1ProfileItsmeResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailResponse, PutEnecoBeXapiSiteApiV1ProfileEmailData, PutEnecoBeXapiSiteApiV1ProfileEmailError, PutEnecoBeXapiSiteApiV1ProfileEmailResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteResponse, PutEnecoBeXapiSiteApiV1AccountsLinkChildData, PutEnecoBeXapiSiteApiV1AccountsLinkChildError, PutEnecoBeXapiSiteApiV1AccountsLinkChildResponse, GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityData, GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityError, GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfData, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfError, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkData, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkError, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkResponse, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeData, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeError, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeResponse, GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameData, GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameError, GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameResponse, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameData, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameError, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyData, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyError, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsData, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsError, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsData, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsError, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdData, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdError, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdResponse, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdData, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdError, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdResponse, PostEnecoBeXapiSiteApiV1ContactContactFormData, PostEnecoBeXapiSiteApiV1ContactContactFormError, PostEnecoBeXapiSiteApiV1ContactContactFormResponse, PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentData, PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentError, PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentResponse, DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdData, DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdError, DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdData, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdError, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsResponse, GetEnecoBeXapiSiteApiV1PricingDynamicData, GetEnecoBeXapiSiteApiV1PricingDynamicError, GetEnecoBeXapiSiteApiV1PricingDynamicResponse, GetEnecoBeXapiSiteApiV1EligibilitiesError, GetEnecoBeXapiSiteApiV1EligibilitiesResponse, PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignData, PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignError, PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsResponse, PostEnecoBeXapiSiteApiV1FormsByFormNameData, PostEnecoBeXapiSiteApiV1FormsByFormNameError, PostEnecoBeXapiSiteApiV1FormsByFormNameResponse, PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryData, PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryError, PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryResponse, PostEnecoBeXapiSiteApiV1ImpersonationInitiateData, PostEnecoBeXapiSiteApiV1ImpersonationInitiateError, PostEnecoBeXapiSiteApiV1ImpersonationInitiateResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveData, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveError, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileData, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileError, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsData, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsError, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsResponse, PostEnecoBeXapiSiteApiV1OrdersValidateData, PostEnecoBeXapiSiteApiV1OrdersValidateError, PostEnecoBeXapiSiteApiV1OrdersValidateResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsData, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsError, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityData, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityError, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationData, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationError, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationResponse, PostEnecoBeXapiSiteApiV1SmartEnergyOfferData, PostEnecoBeXapiSiteApiV1SmartEnergyOfferError, PostEnecoBeXapiSiteApiV1SmartEnergyOfferResponse, GetEnecoBeXapiSiteApiV1SmartEnergyCombinationsError, GetEnecoBeXapiSiteApiV1SmartEnergyCombinationsResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsData, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsError, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsData, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsError, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsResponse, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdData, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdError, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkData, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkError, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkResponse, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkData, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkError, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusData, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusError, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusResponse } from './types.gen';

export const client = createClient(createConfig());

/**
 * Retrieves the current user's profile details.
 */
export const getEnecoBeXapiSiteApiV1Profile = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1ProfileResponse, GetEnecoBeXapiSiteApiV1ProfileError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/profile'
}); };

/**
 * Updates the current user's local login settings
 */
export const putEnecoBeXapiSiteApiV1ProfileLocal = <ThrowOnError extends boolean = false>(options?: Options<PutEnecoBeXapiSiteApiV1ProfileLocalData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1ProfileLocalResponse, PutEnecoBeXapiSiteApiV1ProfileLocalError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/profile/local'
}); };

/**
 * Link a given ItsMe id to the current identity
 */
export const postEnecoBeXapiSiteApiV1ProfileItsme = <ThrowOnError extends boolean = false>(options?: Options<PostEnecoBeXapiSiteApiV1ProfileItsmeData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1ProfileItsmeResponse, PostEnecoBeXapiSiteApiV1ProfileItsmeError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/profile/itsme'
}); };

/**
 * Unlink a given ItsMe id to the current identity
 */
export const deleteEnecoBeXapiSiteApiV1ProfileItsme = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1ProfileItsmeResponse, DeleteEnecoBeXapiSiteApiV1ProfileItsmeError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/profile/itsme'
}); };

/**
 * Allows the user to initiate a forgot email flow for a specific account number
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmail = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberForgotEmailError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/forgot-email'
}); };

/**
 * Updates the current user's email based on a session token received in an email
 */
export const putEnecoBeXapiSiteApiV1ProfileEmail = <ThrowOnError extends boolean = false>(options?: Options<PutEnecoBeXapiSiteApiV1ProfileEmailData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1ProfileEmailResponse, PutEnecoBeXapiSiteApiV1ProfileEmailError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/profile/email'
}); };

/**
 * Lists the account details of a crm account from the viewpoint of the logged in user
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumber = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}'
}); };

/**
 * Updates the account details of a crm account from the viewpoint of the logged in user
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumber = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}'
}); };

/**
 * Get the account contact details
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contact/details'
}); };

/**
 * Update the account contact details
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetailsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contact/details'
}); };

/**
 * Get the contact preferences for a specific crm account.
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferences = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contact/preferences'
}); };

/**
 * Update the account contact preferences
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferences = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContactPreferencesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contact/preferences'
}); };

/**
 * Update the account company details
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetails = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberCompanyDetailsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/company/details'
}); };

/**
 * Links an owner account
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwner = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOwnerError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/owner'
}); };

/**
 * Creates an invitation for a child account
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInvite = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/child/invite'
}); };

/**
 * Based on a token that resides in the URL of an invite email, a child account gets invited
 */
export const putEnecoBeXapiSiteApiV1AccountsLinkChild = <ThrowOnError extends boolean = false>(options?: Options<PutEnecoBeXapiSiteApiV1AccountsLinkChildData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsLinkChildResponse, PutEnecoBeXapiSiteApiV1AccountsLinkChildError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/link/child'
}); };

/**
 * Checks the session token that resides in the URL of an invite email validity
 */
export const getEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidity = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityResponse, GetEnecoBeXapiSiteApiV1AccountsLinkChildInviteBySessionTokenValidityError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/link/child/invite/{sessionToken}/validity'
}); };

/**
 * Get the linked accounts overview
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverview = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkOverviewError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/overview'
}); };

/**
 * Resend email for a child invite
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResend = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkChildInviteByInviteReferenceResendError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/child/invite/{inviteReference}/resend'
}); };

/**
 * Remove the link between the authenticated and the selected crm account. Only available for Read (Lezer) / Write (Mede eigenaar)
 */
export const deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelf = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkSelfError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/self'
}); };

/**
 * Remove the link between the authenticated user and the selected crm account. Only available for Owner
 */
export const deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLink = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/{targetLink}'
}); };

/**
 * Updates the link between the authenticated user and the selected crm account. Only available for Owner
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLink = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberLinkByTargetLinkError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/link/{targetLink}'
}); };

/**
 * Gets city suggestions based on zipCode (part)
 */
export const getEnecoBeXapiSiteApiV1AddressesSearchOnZipcode = <ThrowOnError extends boolean = false>(options?: Options<GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeResponse, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/addresses/searchOnZipcode'
}); };

/**
 * Gets city suggestions based on city name (part)
 */
export const getEnecoBeXapiSiteApiV1AddressesSearchOnCityname = <ThrowOnError extends boolean = false>(options?: Options<GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameResponse, GetEnecoBeXapiSiteApiV1AddressesSearchOnCitynameError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/addresses/searchOnCityname'
}); };

/**
 * Gets street suggestions based on zipCode and street name (part)
 */
export const getEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetname = <ThrowOnError extends boolean = false>(options?: Options<GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameResponse, GetEnecoBeXapiSiteApiV1AddressesSearchOnZipcodeAndStreetnameError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/addresses/searchOnZipcodeAndStreetname'
}); };

/**
 * Get advance amounts for a specific address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/advance-amounts'
}); };

/**
 * Propose a new advance payment amount for all EANs of a delivery address or one specific EAN
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmounts = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/advance-amounts'
}); };

/**
 * Simulate a global amount
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobal = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsSimulateGlobalError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/advance-amounts/simulate/global'
}); };

/**
 * Propose a new vacancy for all EANs on a delivery address
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancy = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/advance-amounts/vacancy'
}); };

/**
 * Delete vacancy for all EANs on a delivery address
 */
export const deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancy = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierAdvanceAmountsVacancyError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/advance-amounts/vacancy'
}); };

/**
 * Gets all assets for an account
 */
export const getEnecoBeXapiSiteApiV1CustomersByCustomerIdAssets = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/assets'
}); };

/**
 * Create a new asset
 */
export const postEnecoBeXapiSiteApiV1CustomersByCustomerIdAssets = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/assets'
}); };

/**
 * Gets a specific asset for a given customer
 */
export const getEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetId = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/assets/{assetId}'
}); };

/**
 * Delete an existing asset
 */
export const deleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetId = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdResponse, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdAssetsByAssetIdError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/assets/{assetId}'
}); };

/**
 * Gets dynamic pricing for a date range
 * Requires a RecaptchaToken in the ContactFormDto body if the
 * Dc.FeatureManagement.FeatureManagementKeys.ContactFormReCaptchaProtection feature flag is enabled
 */
export const postEnecoBeXapiSiteApiV1ContactContactForm = <ThrowOnError extends boolean = false>(options?: Options<PostEnecoBeXapiSiteApiV1ContactContactFormData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1ContactContactFormResponse, PostEnecoBeXapiSiteApiV1ContactContactFormError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/contact/contact/form'
}); };

/**
 * Upload attachment for the contact form
 */
export const postEnecoBeXapiSiteApiV1ContactContactFormAttachment = <ThrowOnError extends boolean = false>(options?: Options<PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentResponse, PostEnecoBeXapiSiteApiV1ContactContactFormAttachmentError, ThrowOnError>({
    ...options,
    ...formDataBodySerializer,
    headers: {
        'Content-Type': null
    },
    url: '/eneco-be/xapi/site/api/v1/contact/contact/form/attachment'
}); };

/**
 * Upload attachment for the contact form
 */
export const deleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentId = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdResponse, DeleteEnecoBeXapiSiteApiV1ContactContactFormAttachmentByAttachmentIdError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/contact/contact/form/attachment/{attachmentId}'
}); };

/**
 * List the product for a given energytype on a contract
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProducts = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contracts/{contractNumber}/products'
}); };

/**
 * Updates the meter details for a given product in a contract
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeter = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsByContractNumberProductsMeterError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contracts/{contractNumber}/products/meter'
}); };

/**
 * Change the current invoice frequency
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequency = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberContractsInvoiceFrequencyError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/contracts/invoice-frequency'
}); };

/**
 * Lookup customer info for a given customer
 */
export const getEnecoBeXapiSiteApiV1CustomersByCustomerId = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}'
}); };

/**
 * Lists all addresses that have (had) at least one contract for the current user.
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddresses = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses'
}); };

/**
 * Lists all products for all contracts concerning a specific address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProducts = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/products'
}); };

/**
 * Lists all the service delivery points for all contracts concerning a specific address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPointsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/service-delivery-points'
}); };

/**
 * Gets hourly dynamic pricing for a specific day
 */
export const getEnecoBeXapiSiteApiV1PricingDynamic = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1PricingDynamicData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1PricingDynamicResponse, GetEnecoBeXapiSiteApiV1PricingDynamicError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/pricing/dynamic'
}); };

/**
 * Retrieves eligibility characteristics of the current account
 */
export const getEnecoBeXapiSiteApiV1Eligibilities = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1EligibilitiesResponse, GetEnecoBeXapiSiteApiV1EligibilitiesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/eligibilities'
}); };

/**
 * Signs one or more agreements
 */
export const postEnecoBeXapiSiteApiV1EligibilitiesAgreementsSign = <ThrowOnError extends boolean = false>(options?: Options<PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignResponse, PostEnecoBeXapiSiteApiV1EligibilitiesAgreementsSignError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/eligibilities/agreements/sign'
}); };

/**
 * Gets peak values based on specified filters
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptar = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptarError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/captar'
}); };

/**
 * Gets consumption data based on specified filters
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumption = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumptionError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{deliveryAddressId}/consumption'
}); };

/**
 * Get meter readings for specific Meters
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadings = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberMeterReadingsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/meter-readings'
}); };

/**
 * Sends form data to the form service
 * Requires a Dc.XAPI.Site.Web.Controllers.FormController.RecaptchaToken in the JSON body if the Dc.FeatureManagement.FeatureManagementKeys.FormReCaptchaProtection feature flag is enabled
 */
export const postEnecoBeXapiSiteApiV1FormsByFormName = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1FormsByFormNameData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1FormsByFormNameResponse, PostEnecoBeXapiSiteApiV1FormsByFormNameError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/forms/{formName}'
}); };

/**
 * Sends form data to the form service
 * Requires a Dc.XAPI.Site.Web.Controllers.FormController.RecaptchaToken in the JSON body if the Dc.FeatureManagement.FeatureManagementKeys.FormReCaptchaProtection feature flag is enabled
 */
export const postEnecoBeXapiSiteApiV1FormsByFormNameSmartbattery = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryResponse, PostEnecoBeXapiSiteApiV1FormsByFormNameSmartbatteryError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/forms/{formName}/smartbattery'
}); };

/**
 * Impersonate another user
 */
export const postEnecoBeXapiSiteApiV1ImpersonationInitiate = <ThrowOnError extends boolean = false>(options?: Options<PostEnecoBeXapiSiteApiV1ImpersonationInitiateData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1ImpersonationInitiateResponse, PostEnecoBeXapiSiteApiV1ImpersonationInitiateError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/impersonation/initiate'
}); };

/**
 * Lists invoices based on account number
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoices = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/invoices'
}); };

/**
 * Search invoices based on an address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoices = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/invoices'
}); };

/**
 * Get the monthly invoice period for a delivery address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/invoices/invoice-period'
}); };

/**
 * Update the monthly invoice period for a delivery address
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriod = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoicesInvoicePeriodError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/invoices/invoice-period'
}); };

/**
 * Get the general move file info for a delivery address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetail = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveDetailError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/detail'
}); };

/**
 * Update key transfer information
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransfer = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeKeyTransferError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/address/{locationType}/key-transfer'
}); };

/**
 * Update the meter information
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeter = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeMeterError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/address/{locationType}/meter'
}); };

/**
 * Update the contact details
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContact = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveAddressByLocationTypeContactError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/address/{locationType}/contact'
}); };

/**
 * Update the correspondence details
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondence = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveCorrespondenceError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/correspondence'
}); };

/**
 * Cancel a move
 */
export const deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMove = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move'
}); };

/**
 * Update the move reason and initiate the move file process
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasons = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveSetupMoveReasonsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/setup/move-reasons'
}); };

/**
 * Upload EOD
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFile = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileError, ThrowOnError>({
    ...options,
    ...formDataBodySerializer,
    headers: {
        'Content-Type': null
    },
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/eod/{locationType}/eod-file'
}); };

/**
 * Delete uploaded EOD
 */
export const deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFile = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodByLocationTypeEodFileError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/eod/{locationType}/eod-file'
}); };

/**
 * Update new Address details
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddress = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveNewAddressError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/new-address'
}); };

/**
 * Confirm the move file flow
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverview = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveConfirmationOverviewError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/confirmation-overview'
}); };

/**
 * Finalize the EOD step
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEod = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierMoveEodError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/move/eod'
}); };

/**
 * Get available products for a given customer
 */
export const getEnecoBeXapiSiteApiV1CustomersByCustomerIdProducts = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdProductsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/products'
}); };

/**
 * Validate order for a given customer
 */
export const postEnecoBeXapiSiteApiV1OrdersValidate = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1OrdersValidateData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1OrdersValidateResponse, PostEnecoBeXapiSiteApiV1OrdersValidateError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/orders/validate'
}); };

/**
 * Get product details based on account and contract numbers
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/payment-methods'
}); };

/**
 * Change the payment method
 */
export const putEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethods = <ThrowOnError extends boolean = false>(options: Options<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsData, ThrowOnError>) => { return (options?.client ?? client).put<PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsResponse, PutEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentMethodsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/payment-methods'
}); };

/**
 * Exports payment transactions into a CSV file
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExport = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierPaymentTransactionsExportError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/payment-transactions/export'
}); };

/**
 * Get payment plans of an account
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlans = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/payment-plans'
}); };

/**
 * Checks whether a payment plan can be created for an account or not
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibility = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibilityError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/payment-plans/eligibility'
}); };

/**
 * Simulate a payment plan for an account
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulation = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulationError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/billing-accounts/{billingAccountNumber}/payment-plans/simulation'
}); };

/**
 * Create a new payment plan for an account
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlans = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/billing-accounts/{billingAccountNumber}/payment-plans'
}); };

/**
 * Get product switch options for all products on a delivery address
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/product-switch'
}); };

/**
 * Initiate a product switch
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/product-switch'
}); };

/**
 * Initiate a product switch
 */
export const deleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitch = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchResponse, DeleteEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierProductSwitchError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/delivery-addresses/{addressIdentifier}/product-switch'
}); };

/**
 * Get product switch options for a siebel order
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderId = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberProductSwitchOrderByOrderIdError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/product-switch/order/{orderId}'
}); };

/**
 * Get meter readings for specific meters
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadings = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationMeterReadingsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/invoices/{invoiceNumber}/rectification/meter-readings'
}); };

/**
 * Get for delivery addresses for an invoice
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddresses = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeliveryAddressesError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/invoices/{invoiceNumber}/rectification/delivery-addresses'
}); };

/**
 * Get for Rectification eligibility
 */
export const getEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibility = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityResponse, GetEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationEligibilityError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/invoices/{invoiceNumber}/rectification/eligibility'
}); };

/**
 * Get for Rectification Deviation
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviation = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationDeviationError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/invoices/{invoiceNumber}/rectification/deviation'
}); };

/**
 * Submit the rectification form
 */
export const postEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectification = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationResponse, PostEnecoBeXapiSiteApiV1AccountsByAccountNumberInvoicesByInvoiceNumberRectificationError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/accounts/{accountNumber}/invoices/{invoiceNumber}/rectification'
}); };

/**
 * Get offer proposal
 */
export const postEnecoBeXapiSiteApiV1SmartEnergyOffer = <ThrowOnError extends boolean = false>(options?: Options<PostEnecoBeXapiSiteApiV1SmartEnergyOfferData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1SmartEnergyOfferResponse, PostEnecoBeXapiSiteApiV1SmartEnergyOfferError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/smart-energy/offer'
}); };

/**
 * Get device combinations
 */
export const getEnecoBeXapiSiteApiV1SmartEnergyCombinations = <ThrowOnError extends boolean = false>(options?: Options<unknown, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1SmartEnergyCombinationsResponse, GetEnecoBeXapiSiteApiV1SmartEnergyCombinationsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/smart-energy/combinations'
}); };

/**
 * Lookup active subscriptions with optional type and tier query parameters for a given customer
 */
export const getEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptions = <ThrowOnError extends boolean = false>(options: Options<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsData, ThrowOnError>) => { return (options?.client ?? client).get<GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsResponse, GetEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/subscriptions'
}); };

/**
 * Register a new subscription that is unique
 */
export const postEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptions = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/subscriptions'
}); };

/**
 * Cancel an existing active subscription
 */
export const deleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionId = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdResponse, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/subscriptions/{subscriptionId}'
}); };

/**
 * Link an existing asset to an existing subscription
 */
export const postEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLink = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdLinkError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/subscriptions/{subscriptionId}/assets/{assetId}/link'
}); };

/**
 * Link an existing asset to an existing subscription
 */
export const deleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlink = <ThrowOnError extends boolean = false>(options: Options<DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkData, ThrowOnError>) => { return (options?.client ?? client).delete<DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkResponse, DeleteEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdAssetsByAssetIdUnlinkError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/subscriptions/{subscriptionId}/assets/{assetId}/unlink'
}); };

/**
 * Update status of an existing subscription
 */
export const postEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatus = <ThrowOnError extends boolean = false>(options: Options<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusData, ThrowOnError>) => { return (options?.client ?? client).post<PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusResponse, PostEnecoBeXapiSiteApiV1CustomersByCustomerIdSubscriptionsBySubscriptionIdStatusError, ThrowOnError>({
    ...options,
    url: '/eneco-be/xapi/site/api/v1/customers/{customerId}/subscriptions/{subscriptionId}/status'
}); };