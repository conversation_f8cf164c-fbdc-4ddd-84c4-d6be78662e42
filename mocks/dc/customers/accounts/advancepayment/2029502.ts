import deepmerge from 'deepmerge';

import { ResponseModels_Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 } from '@monorepo-types/dc';

import { advancePaymentAdviceResponse as defaultData } from './default';

const newData: ResponseModels_Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 = {
  data: {
    metersCombination: 'unknown',
    current: 160,
    advice: 185,
    adviceStatus: 'adviceTooLow',
    cashBackAmount: 0,
    forecastRange: {
      min: 320,
      max: 370,
    },
    statusToggle: {
      showAdvice: true,
      showForecast: true,
    },
    limits: {
      maximumRange: 185,
      minimumRange: 45,
      minimumRangeWarning: 75,
      maximumRangeWarning: 180,
      updateStatus: 'canUpdate',
    },
    usagesCostsTotal: 100,
    alreadyPassedUsagesCostsTotal: 20,
    remainingUsagesCostsTotal: 80,
    advancePaymentAmountTotal: 185,
    alreadyPassedAdvancePaymentAmountTotal: 85,
    remainingAdvancePaymentAmountTotal: 100,
    differenceUsagesCostsTotalAndAdvancePaymentAmountTotal: -85, // seems to be usagesCostsTotal - alreadyPassedAdvancePaymentAmountTotal
    increaseWillBePermanentAboveMinimumRange: false,
  },
};

export const advancePaymentAdviceResponse: ResponseModels_Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 =
  deepmerge(defaultData, newData);
