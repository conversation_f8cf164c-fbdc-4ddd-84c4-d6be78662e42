import deepmerge from 'deepmerge';

import { ResponseModels_Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 } from '@monorepo-types/dc';

import { advancePaymentAdviceResponse as defaultData } from './default';

const newData: ResponseModels_Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 = {
  data: {
    metersCombination: 'unknown',
    current: 185,
    advice: 180,
    forecastRange: {
      min: -25,
      max: -30,
    },
    adviceStatus: 'ok',
    cashBackAmount: 0,
    statusToggle: {
      showAdvice: true,
      showForecast: true,
    },
    usagesCostsTotal: 185,
    alreadyPassedUsagesCostsTotal: 20,
    remainingUsagesCostsTotal: 165,
    advancePaymentAmountTotal: 100,
    alreadyPassedAdvancePaymentAmountTotal: 85,
    remainingAdvancePaymentAmountTotal: 15,
    differenceUsagesCostsTotalAndAdvancePaymentAmountTotal: 85, // seems to be usagesCostsTotal - alreadyPassedAdvancePaymentAmountTotal
    increaseWillBePermanentAboveMinimumRange: false,
  },
};

export const advancePaymentAdviceResponse: ResponseModels_Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2 =
  deepmerge(defaultData, newData);
