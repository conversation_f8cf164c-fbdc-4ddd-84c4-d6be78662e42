import { http, HttpResponse } from 'msw';

import env from '@common/env';

const host = env('DC_HOST');

export default [
  // See the service in `/libs/dc/services/SubscriptionService` to find the endpoint
  http.patch(
    `${host}/dxpweb/:businessUnit/:label/customers/:customerId/subscriptions/products/status`,
    ({ params }) => {
      const { customerId } = params;
      if (customerId) {
        return new HttpResponse(null, { status: 204 });
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    },
  ),
];
