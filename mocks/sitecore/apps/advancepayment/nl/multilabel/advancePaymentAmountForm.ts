const advancePaymentAmountForm = (basePath: string) => ({
  params: { anchor: { value: '' } },
  componentName: 'AdvancePaymentAmountForm',
  fields: {
    title: {
      value: 'Termijnbedrag aanpassen',
    },
    content: {
      value: 'Je kunt je termijnbedrag altijd verhogen of juist verlagen',
    },
    saveButtonText: {
      value: 'Opslaan',
    },
    currentAmountText: {
      value: 'Dit betaal je per maand:',
    },
    advancePaymentAmountLabel: {
      value: 'Termijnbedrag',
    },
    noAmountFieldErrorText: {
      value: 'Vul een nieuw termijnbedrag in.',
    },
    noAmountTitle: {
      value: 'Je termijnbedrag is niet aangepast',
    },
    cannotUpdateBackText: { value: 'Ga terug naar ' },
    cannotUpdateBackLink: {
      value: {
        href: `${basePath}/termijnbedrag/`,
        text: 'termijnbedrag',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{989F39B1-0933-45B0-BE25-43851133499C}',
      },
    },
    cannotUpdateMinimumTitle: { value: 'Je gekozen termijnbedrag is te laag' },
    cannotUpdateMinimumDescription: {
      value: '<p>Je nieuwe termijnbedrag moet minimaal {minimumRange} zijn. <button>Lees meer</button>.</p>',
    },
    cannotUpdateMinimumRangeExplanationDialog: {
      value: {
        title: 'Waarom een minimaal termijnbedrag?',
        content:
          'Je maandelijkse termijnbedrag is berekend op basis van jouw persoonlijke energietarieven en je verwachte energieverbruik door het jaar heen. Als je termijnbedrag te laag is, is de kans groter dat je op de jaarnota veel moet bijbetalen. Om dit te voorkomen, is er een minimaal termijnbedrag van {minimumRange} voor je berekend.',
        triggerText: '',
        submitButtonText: null,
        cancelButtonText: null,
      },
    },
    cannotUpdateMaximumTitle: { value: 'Je kan je termijnbedrag niet verder verhogen' },
    cannotUpdateMaximumDescription: {
      value:
        '<p>We willen voorkomen dat je te veel betaalt. Heb je hier vragen over? Neem dan contact op met onze <button data-chattopic="start_contact_algemeen">klantenservice</button>.</p>',
    },
    cannotUpdateTooLowErrorTitle: { value: 'Je termijnbedrag is te laag' },
    cannotUpdateTooLowErrorDescription: {
      value:
        '<p>Je kan het gekozen termijnbedrag niet opslaan, omdat het te laag is. We willen voorkomen dat je verrast wordt door een hoge jaarnota. Heb je hier vragen over? Neem dan contact op met onze <button data-chattopic="start_me_termijnbedrag_verlagen">klantenservice</button>.</p>',
    },
    cannotUpdateTooHighErrorTitle: { value: 'Je termijnbedrag is te hoog' },
    cannotUpdateTooHighErrorDescription: {
      value:
        '<p>Je kan het gekozen termijnbedrag niet opslaan, omdat het te hoog is. We willen voorkomen dat je te veel betaalt. Heb je hier vragen over? Neem dan contact op met onze <button data-chattopic="start_contact_algemeen">klantenservice</button>.</p>',
    },
    cannotUpdateTitle: {
      value: 'Je kan je termijnbedrag nu niet aanpassen',
    },
    formFeedbackSuccessText: { value: 'Je wijzigingen zijn successvol opgeslagen' },
    formFeedbackEstimatedMeterReadingsErrorDescription: {
      value:
        'Je kan je termijnbedrag niet verlagen, omdat je laatste jaarlijkse factuur gebaseerd is op geschatte meterstanden.',
    },
    formFeedbackMultipleAccountsErrorDescription: {
      value:
        'Je kan je termijnbedrag niet verlagen, omdat je meerdere accounts op je naam hebt staan. Neem hiervoor even contact op met onze Klantenservice. We helpen je graag.',
    },
    formFeedbackDefaultErrorDescription: { value: 'Formulier is niet opgeslagen.' },
    cancelLink: {
      value: {
        href: `${basePath}/termijnbedrag/`,
        text: 'Annuleren',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{989F39B1-0933-45B0-BE25-43851133499C}',
      },
    },
    selfserviceInvoicesLink: {
      value: {
        href: `${basePath}/notas/openstaand/`,
        text: "openstaande nota's",
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{989F39B1-0933-45B0-BE25-43851133499C}',
      },
    },
    selfserviceCustomerServiceLink: {
      value: {
        href: '/klantenservice/',
        text: 'Klantenservice',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{989F39B1-0933-45B0-BE25-43851133499C}',
      },
    },
    increaseWillBePermanentTitle: {
      value: 'Belangrijk',
    },
    increaseWillBePermanentDescription: {
      value:
        'Na deze wijziging kan je je termijnbedrag niet meer lager instellen dan €{minimumRange} via de app of Mijn Eneco.',
    },
  },
});

export default advancePaymentAmountForm;
