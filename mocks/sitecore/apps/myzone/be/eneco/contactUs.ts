import { ContactUsRendering } from '@sitecore/types/ContactUs';

const ContactUs: ContactUsRendering = {
  uid: '2e2922e8-1b37-4c0e-96e0-6651fe497a62',
  componentName: 'ContactUs',
  dataSource: '{3C06CF3B-D26B-49E2-B338-************}',
  params: {},
  datasourceRequired: true,
  fields: {
    chatTopicText: {
      value: 'herosearch1',
    },
    companyNameFormField: {
      value: {
        hint: 'Naam van je onderneming',
        label: 'Bedrijfsnaam',
        placeholder: 'Bijv. Eneco BV',
        requiredMessage: 'Bedrijfsnaam is verplicht',
        validationMessage: 'Voer een geldige bedrijfsnaam in',
      },
    },
    companyNumberFormField: {
      value: {
        hint: 'Vul je ondernemingsnummer in',
        label: 'Ondernemingsnummer',
        placeholder: 'Bijv. BE0*********',
        requiredMessage: 'Ondernemingsnummer is verplicht',
        validationMessage: 'Voer een geldig nummer in',
        validationMinValueMessage: 'Minimum waarde is ongeldig',
        validationMaxValueMessage: 'Maximum waarde is ongeldig',
      },
    },
    customerNumberFormField: {
      value: {
        hint: 'Je vindt dit op je factuur',
        label: 'Klantnummer',
        placeholder: 'Bijv. *********',
        requiredMessage: 'Klantnummer is verplicht',
        validationMessage: 'Voer een geldig klantnummer in',
        validationMinValueMessage: 'Minimum waarde is ongeldig',
        validationMaxValueMessage: 'Maximum waarde is ongeldig',
      },
    },
    description: {
      value: '',
    },
    emailFormField: {
      value: {
        hint: 'We gebruiken dit om je te contacteren',
        label: 'E-mailadres',
        placeholder: 'Bijv. <EMAIL>',
        requiredMessage: 'E-mailadres is verplicht',
        validationMessage: 'Voer een geldig e-mailadres in',
      },
    },
    errorNotification: {
      value: {
        title: 'Er is iets misgegaan',
        content: 'We konden je verzoek niet verwerken. Probeer het later opnieuw.',
        variant: 'error',
      },
    },
    fileUploadFormField: {
      value: {
        hint: 'Sleep hier je bestanden naartoe of klik om te uploaden',
        label: 'Document uploaden',
        placeholder: '',
        requiredMessage: 'Bestand uploaden is verplicht',
        validationMessage: 'Ongeldig bestandstype of te groot',
      },
    },
    fileUploadTitle: {
      value: 'Upload je documenten (optioneel)',
    },
    firstNameFormField: {
      value: {
        hint: 'Vul je voornaam in',
        label: 'Voornaam',
        placeholder: 'Bijv. Jan',
        requiredMessage: 'Voornaam is verplicht',
        validationMessage: 'Voer een geldige voornaam in',
      },
    },
    lastNameFormField: {
      value: {
        hint: 'Vul je achternaam in',
        label: 'Achternaam',
        placeholder: 'Bijv. Jansen',
        requiredMessage: 'Achternaam is verplicht',
        validationMessage: 'Voer een geldige achternaam in',
      },
    },
    link: {
      value: {
        anchor: '',
        class: '',
        href: '/inloggen',
        id: '',
        linktype: 'internal',
        querystring: '',
        target: '',
        text: 'Log in met je Eneco-account',
        title: '',
        url: '/inloggen',
      },
    },
    phoneNumberFormField: {
      value: {
        hint: 'Vul je telefoonnummer in',
        label: 'Telefoonnummer',
        placeholder: 'Bijv. 0470 12 34 56',
        requiredMessage: 'Telefoonnummer is verplicht',
        validationMessage: 'Voer een geldig telefoonnummer in',
      },
    },
    privacyStatementContent: {
      value: 'Je gegevens worden verwerkt volgens ons privacybeleid.',
    },
    privacyStatementLink: {
      value: {
        anchor: '',
        class: '',
        href: 'https://example.com',
        id: '',
        linktype: 'external',
        querystring: '',
        target: '',
        text: 'privacy link',
        title: '',
        url: 'https://example.com',
      },
    },
    professionalUserFormField: {
      value: {
        hint: '',
        label: 'Ben je een zakelijke klant?',
        requiredMessage: 'Maak een keuze',
        options: [
          {
            description: 'Ik ben een particuliere klant',
            label: 'Nee',
            name: 'No',
            value: 'no',
          },
          {
            description: 'Ik ben een professionele klant',
            label: 'Ja',
            name: 'Yes',
            value: 'yes',
          },
        ],
      },
    },
    questionFormField: {
      value: {
        hint: 'Omschrijf duidelijk je vraag of probleem',
        label: 'Je vraag',
        placeholder: 'Bijv. Ik heb een vraag over mijn factuur...',
        requiredMessage: 'Je vraag is verplicht',
        validationMessage: 'Voer een geldige vraag in',
      },
    },
    sendButtonLink: {
      value: {
        anchor: '',
        class: '',
        href: '/bedankt-contact',
        id: '',
        linktype: 'internal',
        querystring: '',
        target: '',
        text: 'Verstuur',
        title: '',
        url: '/bedankt-contact',
      },
    },
    subjectFormField: {
      value: {
        hint: 'Kies het onderwerp van je vraag',
        label: 'Onderwerp',
        placeholder: 'Bijv. Factuur, Verhuis...',
        requiredMessage: 'Onderwerp is verplicht',
        validationMessage: 'Voer een geldig onderwerp in',
      },
    },
    successNotification: {
      value: {
        title: 'Verzonden',
        content: 'Je aanvraag is succesvol verzonden. We nemen spoedig contact met je op.',
        variant: 'success',
      },
    },
    text: {
      value: 'Ben je al klant? Log in om sneller geholpen te worden.',
    },
    title: {
      value: 'Inloggen voor klanten',
    },
    yourDataSubTitle: {
      value: 'Vul hieronder je persoonlijke gegevens in',
    },
    yourDataTitle: {
      value: 'Jouw gegevens',
    },
    yourQuestionTitle: {
      value: 'Wat is je vraag?',
    },
  },
};

export default ContactUs;
