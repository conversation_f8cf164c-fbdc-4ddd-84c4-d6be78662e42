import { SelectMoveReasonRendering } from '@sitecore/types/SelectMoveReason';

const selectMoveReason: SelectMoveReasonRendering = {
  uid: '7CA18F1F-8D2B-47EC-803B-4365438B2CD1',
  componentName: 'SelectMoveReason',
  dataSource: '74BF9F1A-7676-4E9C-B6DD-80357FCAF3CF',
  params: {},
  datasourceRequired: false,
  fields: {
    content: {
      actionDescription: {
        value:
          "Zodra je op 'Volgende' klikt, worden je gegevens automatisch opgeslagen. Dit betekent dat je op elk moment kunt pauzeren en later weer verder kunt gaan met het aanmaken van je verhuisdossier.",
      },
      cancelButtonLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: 'internal',
          querystring: '',
          target: '',
          text: 'Ann<PERSON><PERSON>',
          title: '',
          url: '',
        },
      },
      cancelFlowButtonLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: 'internal',
          querystring: '',
          target: '',
          text: '',
          title: '',
          url: '',
        },
      },
      continueFlowButtonLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: 'internal',
          querystring: '',
          target: '',
          text: '',
          title: '',
          url: '',
        },
      },
      description: {
        value:
          'Kies de optie die het beste past bij uw verhuissituatie. Dit helpt ons uw verhuizing beter te ondersteunen.',
      },
      dialog: {
        value: {
          cancelButtonText: 'Ik wil mijn contract beëindigen',
          content:
            'Wist je dat je energiecontract op jouw naam staat en niet gekoppeld is aan je adres? Je kan gemakkelijk van al je Eneco voordelen blijven genieten op je nieuwe adres',
          submitButtonText: 'Ik blijf klant bij Eneco',
          title: 'Blijf genieten van onze voordelen op je nieuwe adres',
          triggerText: '',
        },
      },
      errorNotification: {
        value: {
          content: 'Er is een fout opgetreden. Probeer het later opnieuw.',
          title: 'Er is een fout opgetreden',
          variant: 'error',
        },
      },
      moveOptionsList: {
        value: {
          enum: [
            {
              label: 'Ik ga verhuizen en wil Eneco meenemen',
              name: 'MoveAndKeepEneco',
              value: 'MoveAndKeepEneco',
            },
            {
              label: 'Ik ga verhuizen en wil bij iemand anders intrekken',
              name: 'MoveInWithSomeoneElse',
              value: 'MoveInWithSomeoneElse',
            },
            {
              label: 'Ik ga verhuizen naar een zorginstelling',
              name: 'MoveIntoResidentialCareCenter',
              value: 'MoveIntoResidentialCareCenter',
            },
            {
              label: 'Ik ga verhuizen naar het buitenland',
              name: 'MoveToOtherCountry',
              value: 'MoveToOtherCountry',
            },
            {
              label: 'Ik ga verhuizen en wil Eneco opzeggen',
              name: 'MoveAndLeaveEneco',
              value: 'MoveAndLeaveEneco',
            },
            {
              label: 'Andere (chat met ons)',
              name: 'Other',
              value: 'Other',
            },
          ],
        },
      },
      nextButtonLink: {
        value: {
          anchor: '',
          class: '',
          href: '',
          id: '',
          linktype: 'internal',
          querystring: '',
          target: '',
          text: 'Volgende',
          title: '',
          url: '',
        },
      },
      requiredText: {
        value: 'Dit veld is verplicht',
      },
      title: {
        value: 'Wat is jouw verhuissituatie?',
      },
    },
  },
};
export default selectMoveReason;
