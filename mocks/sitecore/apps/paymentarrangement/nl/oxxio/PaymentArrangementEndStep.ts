import { PaymentArrangementEndStepRendering } from '@sitecore/types/PaymentArrangementEndStep';

const PaymentArrangementStartDateStep: PaymentArrangementEndStepRendering = {
  params: { anchor: { value: '' } },
  componentName: 'PaymentArrangementStartDateStep',
  uid: 'M5C4aX',
  dataSource: '',
  datasourceRequired: false,
  fields: {
    image: {
      value: {
        src: 'https://images.placeholders.dev/?width=1024&height=460&bgColor=%23FEE6E6&textColor=%23EEA7A7&fontSize=70',
        alt: 'Person holding document illustration',
        width: '430',
        height: '198',
        vspace: '0',
        hspace: '0',
      },
    },
    title: { value: 'Gelukt!' },
    content: {
      value: 'Je betalingsregeling is succesvol aangevraagd.',
    },
    cta: {
      value: {
        href: '/mijn-eneco/notas/',
        text: 'Terug naar Nota-overzicht',
        linktype: 'internal',
        anchor: '',
        target: '',
        class: '',
        title: '',
        querystring: '',
        id: '',
        url: '/mijn-eneco/notas/',
      },
    },
  },
};

export default PaymentArrangementStartDateStep;
