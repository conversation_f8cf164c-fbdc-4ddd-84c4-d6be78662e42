import { PaymentArrangementEndStepRendering } from '@sitecore/types/PaymentArrangementEndStep';

const PaymentArrangementStartDateStep: PaymentArrangementEndStepRendering = {
  params: { anchor: { value: '' } },
  componentName: 'PaymentArrangementStartDateStep',
  uid: 'M5C4aX',
  dataSource: '',
  datasourceRequired: false,
  fields: {
    image: {
      value: {
        src: 'https://www.test.eneco.nl/-/media/eneco-nl/illustrations/image-430.png?h=198&iar=0&w=430&hash=E54B9C0C731F3593A9605384C4FDB616',
        alt: 'Person holding document illustration',
        width: '430',
        height: '198',
        vspace: '0',
        hspace: '0',
      },
    },
    title: { value: 'Gelukt!' },
    content: {
      value: 'Je betalingsregeling is succesvol aangevraagd.',
    },
    cta: {
      value: {
        href: '/mijn-eneco/notas/',
        text: 'Terug naar Nota-overzicht',
        linktype: 'internal',
        anchor: '',
        target: '',
        class: '',
        title: '',
        querystring: '',
        id: '',
        url: '/mijn-eneco/notas/',
      },
    },
  },
};

export default PaymentArrangementStartDateStep;
