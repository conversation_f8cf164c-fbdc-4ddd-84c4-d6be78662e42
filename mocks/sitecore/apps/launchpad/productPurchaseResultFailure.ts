import { ProductPurchaseResultRendering } from '@sitecore/types/ProductPurchaseResult';

const productPurchaseResultFailure: ProductPurchaseResultRendering = {
  params: { anchor: { value: '' } },
  uid: '2235a6f3-80f6-4e48-a498-f525a5209fc',
  dataSource: '{4337E813-BDDD-4CA4-AA07-3862E43B6DC6}',
  datasourceRequired: false,
  componentName: 'ProductPurchaseResult',
  fields: {
    resultImage: {
      value: {
        alt: '',
        formats: [
          {
            format: 'default',
            src: 'https://placehold.co/180x180',
            height: '180px',
            width: '180px',
          },
        ],
      },
    },
    resultTitel: { value: 'We kunnen je bestelling niet afronden!' },
    resultContent: {
      value:
        'Er liep iets mis waardoor we de bestelling niet kunnen afronden. Probeer opnieuw of contacteer onze klantendienst.',
    },
    resultStatusLink: {
      value: {
        href: '/',
        text: '',
        url: '',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{E9B1DE24-0224-4FCE-A4F5-75D00C8FFABC}',
        target: '',
      },
    },

    resultCTALink: {
      value: {
        href: '/?item=%2dashboard',
        text: 'Terug naar de product pagina',
        url: '',
        anchor: '',
        linktype: 'internal',
        class: '',
        title: '',
        querystring: '',
        id: '{E9B1DE24-0224-4FCE-A4F5-75D00C8FF749}',
        target: '',
      },
    },
    donglePurchasedContent: {
      value: '',
    },
    donglePurchasedPrimaryButtonLink: {
      value: {
        class: '',
        querystring: '',
        id: '{E9B1DE24-0224-4FCE-A4F5-75D00C8FF899}',
        href: '',
        text: '',
        linktype: 'external',
        url: '',
        anchor: '',
        title: '',
        target: '',
      },
    },
    donglePurchasedTitle: {
      value: '',
    },
    donglePurchasedSecondaryButtonLink: {
      value: {
        class: '',
        querystring: '',
        id: '{E9B1DE24-0224-4FCE-A4F5-75D00C8FF899}',
        href: '',
        text: '',
        linktype: 'external',
        url: '',
        anchor: '',
        title: '',
        target: '',
      },
    },
  },
};

export default productPurchaseResultFailure;
