import { LayoutServiceData } from '@sitecore/types/lib';

import cookieWall from '../../../apps/cookiewall/nl/shared/cookieWall';
import accountInviteForm from '../../../apps/myzone/be/eneco/accountInviteForm';
import accountLinkDialog from '../../../apps/myzone/be/eneco/accountLinkDialog';
import accountOverview from '../../../apps/myzone/be/eneco/accountOverview';
import agreement from '../../../apps/myzone/be/eneco/agreement';
import billingDetails from '../../../apps/myzone/be/eneco/billingDetails';
import comfortBonus from '../../../apps/myzone/be/eneco/comfortBonus';
import contactData from '../../../apps/myzone/be/eneco/contactData';
import contactPreferences from '../../../apps/myzone/be/eneco/contactPreferences';
import contactUs from '../../../apps/myzone/be/eneco/contactUs';
import dashboard from '../../../apps/myzone/be/eneco/dashboard';
import editAdvanceAmount from '../../../apps/myzone/be/eneco/EditAdvanceAmount';
import editChildAccountForm from '../../../apps/myzone/be/eneco/editChildAccountForm';
import editCompanyData from '../../../apps/myzone/be/eneco/editCompanyData';
import editContactData from '../../../apps/myzone/be/eneco/editContactData';
import editCrmAccountDetails from '../../../apps/myzone/be/eneco/editCrmAccountDetails';
import editGezinsbond from '../../../apps/myzone/be/eneco/editGezinsbond';
import editMeterRegime from '../../../apps/myzone/be/eneco/editMeterRegime';
import energyMonitor from '../../../apps/myzone/be/eneco/energyMonitor';
import energyMonitorLegend from '../../../apps/myzone/be/eneco/energyMonitorLegend';
import forgotEmail from '../../../apps/myzone/be/eneco/forgotEmail';
import forgotEmailConfirmed from '../../../apps/myzone/be/eneco/forgotEmailConfirmed';
import impersonation from '../../../apps/myzone/be/eneco/impersonation';
import invoiceEditDueDate from '../../../apps/myzone/be/eneco/invoiceEditDueDate';
import invoiceEditFrequency from '../../../apps/myzone/be/eneco/invoiceEditFrequency';
import invoiceOverview from '../../../apps/myzone/be/eneco/invoiceOverview';
import manageAdvanceAmount from '../../../apps/myzone/be/eneco/manageAdvanceAmount';
import moveBillingAddressStep from '../../../apps/myzone/be/eneco/moveBillingAddressStep';
import moveEditMeterDetails from '../../../apps/myzone/be/eneco/moveEditMeterDetails';
import moveEodUpload from '../../../apps/myzone/be/eneco/moveEodUpload';
import moveFileContactDetails from '../../../apps/myzone/be/eneco/moveFileContactDetails';
import moveFileEditBillingAddress from '../../../apps/myzone/be/eneco/moveFileEditBillingAddress';
import moveFileNewAddressDetails from '../../../apps/myzone/be/eneco/moveFileNewAddressDetails';
import moveFileOldAddressDetails from '../../../apps/myzone/be/eneco/moveFileOldAddressDetails';
import moveInDateStep from '../../../apps/myzone/be/eneco/moveInDateStep';
import newAddressForm from '../../../apps/myzone/be/eneco/moveNewAddressStep';
import moveOnboarding from '../../../apps/myzone/be/eneco/moveOnboarding';
import moveOutDateStep from '../../../apps/myzone/be/eneco/moveOutDateStep';
import moveOutStepIndicator from '../../../apps/myzone/be/eneco/moveOutStepIndicator';
import moveQuestionOverview from '../../../apps/myzone/be/eneco/moveOverview';
import moveResidentChangeContactDetails from '../../../apps/myzone/be/eneco/moveResidentChangeContactDetails';
import selectMoveReason from '../../../apps/myzone/be/eneco/moveSelectReason';
import productDetail from '../../../apps/myzone/be/eneco/productDetail';
import productOverview from '../../../apps/myzone/be/eneco/productOverview';
import productSwitch from '../../../apps/myzone/be/eneco/productSwitch';
import profileManagementLoginDetails from '../../../apps/myzone/be/eneco/profileManagementLoginDetails';
import profileManagementLoginDetailsConfirmEmail from '../../../apps/myzone/be/eneco/profileManagementLoginDetailsConfirmEmail';
import profileManagementLoginDetailsLinkItsme from '../../../apps/myzone/be/eneco/ProfileManagementLoginDetailsLinkItsme';
import profileManagementLoginDetailsUpdate from '../../../apps/myzone/be/eneco/profileManagementLoginDetailsUpdate';
import { prospectCta, prospectCtaWithImage } from '../../../apps/myzone/be/eneco/prospectCta';
import requestVacancy from '../../../apps/myzone/be/eneco/requestVacancy';
import upcomingYearlyInvoice from '../../../apps/myzone/be/eneco/upcomingYearlyInvoice';
import updateInvoicePaymentMethod from '../../../apps/myzone/be/eneco/updateInvoicePaymentMethod';
import hotjar from '../../../apps/tracking/be/eneco/hotjar';
import vwo from '../../../apps/tracking/be/eneco/vwo';
import { getBaseContext } from '../../../common';
import { chat } from '../../../components/be/chat';
import { chatLinks } from '../../../components/be/chatLinks';
import { contactCustomerService } from '../../../components/be/contactCustomerService';
import { dynamicPricing } from '../../../components/be/dynamicPricing';
import EloquaForm from '../../../components/be/eloquaForm';
import { getMinimalNavBar, getNavBar } from '../../../components/be/eneco/navigationBar';
import { flourishChart } from '../../../components/be/flourishChart';
import { projectOverview } from '../../../components/be/projectOverview';
import { slimmeThuisbatterij, slimmeThuisbatterijFields } from '../../../components/be/slimmeThuisbatterij';
import nlChatLinks from '../../../components/shared/chatLinks';
import { getFooter } from '../../../components/shared/footer';
import metaData from '../../../components/shared/metaData';
import section from '../../../components/shared/section';
import { createNavigation } from '../../../components/shared/SideNavigation/helpers';
import structuredDataSchema from '../../../components/shared/structuredDataSchema';
import { Mock } from '../../../types/mock';

const SITECORE_NO_GRID_GUID = '2dfd92c7-5914-4d46-a1ad-7733cfdd9c70';
const SITECORE_THREE_COLUMN_GUID = '9006D52D-19CB-4607-97E1-43E3C90353F5';

const mock: Mock = (item, options = { locale: 'nl-BE', site: 'be-eneco-main' }): LayoutServiceData => {
  const route: LayoutServiceData['sitecore']['route'] = {
    name: 'Eneco',
    displayName: 'Eneco',
    fields: {
      isPreferencePage: { value: true },
      enableAuthentication: { value: false },
      enableGrayBackground: { value: false },
      pageTitle: { value: false },
      pageSubTitle: { value: false },
      allowFrontEndRouting: { value: false },
      frontEndRootPath: { value: '' },
    },
    databaseName: 'web',
    placeholders: {
      'jss-navigationbar': [
        getNavBar({
          includeSubHeader: true,
          includeTopHeader: true,
          includeBreadcrumbs: true,
          label: 'eneco',
          businessUnit: 'be',
          currentRoute: item,
        }),
      ],
      'jss-notifications': [],
      'jss-main': [],
      'jss-meta': [cookieWall(), metaData(), structuredDataSchema(), vwo(), hotjar()],
      'jss-footer': [getFooter(), accountLinkDialog],
    },
  };

  const context = getBaseContext({ domain: 'eneco', basePath: item, ...options });

  if (item.includes('/profile-management/')) {
    route.templateId = SITECORE_THREE_COLUMN_GUID;
    route.placeholders['jss-left'] = [
      createNavigation(
        item,
        item,
        [
          { name: 'Inloggegevens', path: '/my-eneco/profile-management/login-details' },
          { name: 'Account & Gebruikers', path: '/my-eneco/profile-management/account-overview' },
          { name: 'Betaalgegevens', path: '/my-eneco/profile-management/billing-details' },
          { name: 'Contactgegevens', path: '/my-eneco/profile-management/contact-data' },
          { name: 'Contactvoorkeuren', path: '/my-eneco/profile-management/contact-preferences' },
        ],
        'SideNav',
      ),
    ];
  }

  if (item.includes('/my-eneco') && route.fields) {
    route.fields.enableGrayBackground.value = true;
  }

  if (route.fields && item.includes('/my-eneco/profile-management/login-details'))
    route.fields.pageTitle.value = 'Jouw inloggegevens';

  switch (true) {
    case item === '/' || item === '/nl/':
      route.placeholders['jss-main'] = [contactCustomerService, chat];
      break;
    case item === '/kmo-soho/':
      route.placeholders['jss-main'] = [chat];
      break;
    case item.includes('/my-eneco/contact-form'):
      if (route.fields) {
        route.fields.pageTitle.value = 'Factureringsfrequentie';
        route.fields.enableAuthentication.value = false;
      }
      route.placeholders['jss-main'] = [contactUs, chat];
      break;
    case item.includes('/my-eneco/energy-monitor/'):
      route.placeholders['jss-main'] = [energyMonitor, energyMonitorLegend];
      break;
    case item.includes('/my-eneco/profile-management/billing-details/edit-due-date'):
      if (route.fields) route.fields.pageTitle.value = 'Vervaldatum voorschotfacturen';
      route.placeholders['jss-main'] = [invoiceEditDueDate];
      break;
    case item.includes('/my-eneco/profile-management/billing-details/edit-frequency'):
      if (route.fields) route.fields.pageTitle.value = 'Factureringsfrequentie';
      route.placeholders['jss-main'] = [invoiceEditFrequency];
      break;
    case item.includes('/my-eneco/profile-management/billing-details'):
      if (route.fields) route.fields.pageTitle.value = 'Betaalgegevens';
      route.placeholders['jss-main'] = [billingDetails];
      break;
    case item.includes('/my-eneco/profile-management/update-payment-method'):
      if (route.fields) route.fields.pageTitle.value = 'Update betaalgegevens';
      route.placeholders['jss-main'] = [updateInvoicePaymentMethod];
      break;
    case item.includes('/crowdlending/'):
      route.placeholders['jss-main'] = [projectOverview];
      break;
    case item === '/b2b/':
      route.placeholders['jss-main'] = [chat];
      break;
    case item.includes('/my-eneco/dashboard/') || item === '/my-eneco/':
      route.layoutId = SITECORE_NO_GRID_GUID;
      route.placeholders['jss-no-grid'] = [dashboard('/my-eneco/')];
      if (route.fields) route.fields.pageTitle.value = '';
      route.placeholders['jss-main'] = [prospectCtaWithImage];
      break;
    case item.includes('/my-eneco/login/forgot-email/'):
      route.placeholders['jss-no-grid'] = [forgotEmail];
      break;
    case item.includes('/my-eneco/login/forgot-email-confirmed/'):
      route.placeholders['jss-no-grid'] = [forgotEmailConfirmed];
      break;
    case item.includes('/my-eneco/privacy-policy'):
      route.placeholders['jss-main'] = [agreement];
      break;
    case item.includes('/my-eneco/profile-management/account-overview'):
      route.placeholders['jss-main'] = [accountOverview, prospectCta];
      if (route.fields) {
        route.fields.pageTitle.value = 'Account & Gebruikers';
        route.fields.pageSubTitle.value =
          'Hier vind je een overzicht van de mensen met wie je je account deelt, en de mogelijkheid om anderen uit te nodigen voor je account.';
      }
      break;
    case item.includes('/my-eneco/profile-management/child'):
      route.placeholders['jss-main'] = [editChildAccountForm, prospectCta];
      if (route.fields) route.fields.pageTitle.value = 'Gebruiker aanpassen';
      break;
    case item.includes('/my-eneco/profile-management/contact-data/edit'):
      route.placeholders['jss-main'] = [editContactData, prospectCta];
      if (route.fields) route.fields.pageTitle.value = 'Contactgegevens wijzigen';
      break;
    case item.includes('/my-eneco/profile-management/contact-details/family-bond/edit'):
      route.placeholders['jss-main'] = [editGezinsbond];
      if (route.fields) route.fields.pageTitle.value = 'Gezinsbond wijzigen';
      break;
    case item.includes('/my-eneco/profile-management/contact-data/company-data/edit'):
      route.placeholders['jss-main'] = [editCompanyData, prospectCta];
      if (route.fields) route.fields.pageTitle.value = 'Contactgegevens';
      break;
    case item.includes('/my-eneco/profile-management/contact-data'):
      route.placeholders['jss-main'] = [contactData, prospectCta];
      if (route.fields) route.fields.pageTitle.value = 'Contactgegevens';
      break;
    case item.includes('/my-eneco/profile-management/contact-preferences'):
      route.placeholders['jss-main'] = [contactPreferences, prospectCta];
      if (route.fields) route.fields.pageTitle.value = 'Contactvoorkeuren';
      break;
    case item.includes('/my-eneco/profile-management/account/edit'):
      if (route.fields) route.fields.pageTitle.value = 'Account aanpassen';
      route.placeholders['jss-main'] = [editCrmAccountDetails];
      break;
    case item.includes('/my-eneco/account-invite'):
      route.placeholders['jss-main'] = [accountInviteForm];
      break;
    case item.includes('/my-eneco/profile-management/login-details/update/'):
      route.placeholders['jss-main'] = [profileManagementLoginDetailsUpdate];
      break;
    case item.includes('/my-eneco/profile-management/login-details/confirm-email-change/'):
      route.placeholders['jss-main'] = [profileManagementLoginDetailsConfirmEmail];
      break;
    case item.includes('/my-eneco/profile-management/login-details/link-itsme/'):
      route.placeholders['jss-main'] = [profileManagementLoginDetailsLinkItsme];
      break;
    case item.includes('/my-eneco/profile-management/login-details/'):
      route.placeholders['jss-main'] = [profileManagementLoginDetails];
      break;
    case item.includes('/dynamic-pricing/'):
      route.placeholders['jss-main'] = [dynamicPricing];
      break;
    case item.includes('/my-eneco/impersonation/'):
      route.placeholders['jss-main'] = [impersonation];
      break;

    case item.includes('meter-regime/edit'):
      route.templateId = '9006D52D-19CB-4607-97E1-43E3C90353F5';
      route.placeholders['jss-main'] = [editMeterRegime];
      if (route.fields) {
        route.fields.pageTitle.value = 'Meter regime wijzigen';
      }
      break;
    case item.includes('/my-eneco/products/details/'):
      route.placeholders['jss-main'] = [productDetail, prospectCta];
      route.templateId = '9006D52D-19CB-4607-97E1-43E3C90353F5';
      if (route.fields) {
        route.fields.pageTitle.value = 'Product details';
      }
      break;
    case item.includes('/my-eneco/products/switch'):
      route.layoutId = SITECORE_NO_GRID_GUID;
      route.placeholders['jss-main'] = [productSwitch];
      break;
    case item.includes('/my-eneco/products'):
      route.placeholders['jss-main'] = [productOverview, prospectCta];
      route.placeholders['jss-right'] = [comfortBonus];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Producten';
      }
      break;
    case item.includes('/my-eneco/invoices'):
      route.placeholders['jss-main'] = [invoiceOverview];
      route.placeholders['jss-right'] = [upcomingYearlyInvoice];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Facturen';
      }
      break;
    case item.includes('/my-eneco/move/select-reason'):
      route.placeholders['jss-main'] = [selectMoveReason];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;
    case item.includes('/my-eneco/move/contact-details/edit-address'):
      route.placeholders['jss-main'] = [moveFileEditBillingAddress];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Je contactgegevens';
      }
      break;
    case item.includes('/my-eneco/move/cancel/contact-details'):
      route.placeholders['jss-main'] = [moveFileContactDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Je contactgegevens';
      }
      break;
    case item.includes('/my-eneco/move/cancel/edit-meter-details'):
      route.placeholders['jss-main'] = [moveEditMeterDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;
    case item.includes('/my-eneco/move/old-address-details'):
      route.placeholders['jss-main'] = [moveFileOldAddressDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;
    case item.includes('/my-eneco/move/new-address-details'):
      route.placeholders['jss-main'] = [moveFileNewAddressDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/cancel/eod/') || item.includes('/my-eneco/move/continue/eod/'):
      route.placeholders['jss-main'] = [moveOutStepIndicator, moveEodUpload];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/cancel/confirmation-overview/') ||
      item.includes('/my-eneco/move/continue/confirmation-overview/'):
      route.placeholders['jss-main'] = [moveQuestionOverview];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/cancel/move-out-date/') ||
      item.includes('/my-eneco/move/continue/move-out-date/'):
      route.placeholders['jss-main'] = [moveOutDateStep];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/cancel/new-address/') || item.includes('/my-eneco/move/continue/new-address/'):
      route.placeholders['jss-main'] = [newAddressForm];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/cancel/move-in-date/') || item.includes('/my-eneco/move/continue/move-in-date/'):
      route.placeholders['jss-main'] = [moveInDateStep];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/cancel/billing-address/'):
      route.placeholders['jss-main'] = [moveBillingAddressStep];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/contact-details/leaving-resident/'):
      route.placeholders['jss-main'] = [moveResidentChangeContactDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/contact-details/incoming-resident/'):
      route.placeholders['jss-main'] = [moveResidentChangeContactDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;

    case item.includes('/my-eneco/move/edit-meter-details'):
      route.placeholders['jss-main'] = [moveEditMeterDetails];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = '';
      }
      break;
    case item.includes('/my-eneco/move'):
      route.placeholders['jss-main'] = [moveOnboarding];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Verhuis';
      }
      break;
    case item.includes('/my-eneco/voorschotbedrag/edit'):
      route.placeholders['jss-main'] = [editAdvanceAmount];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Voorschotbedrag wijzigen';
      }
      break;
    case item.includes('/my-eneco/voorschotbedrag'):
      route.placeholders['jss-main'] = [manageAdvanceAmount];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Voorschotbedrag';
      }
      break;

    case item.includes('/my-eneco/leegstand'):
      route.placeholders['jss-main'] = [requestVacancy];
      route.templateId = SITECORE_THREE_COLUMN_GUID;
      if (route.fields) {
        route.fields.pageTitle.value = 'Leegstand';
      }
      break;
    case item.includes('/flourish-chart/'):
      route.placeholders['jss-main'] = [flourishChart];
      break;
    case item.includes('/form/'):
      route.placeholders['jss-main'] = [EloquaForm];
      break;
    case item.includes('/chatlinks/'):
      route.placeholders['jss-main'] = [section(chatLinks), section(nlChatLinks('eneco')), chat];
      break;
    case item.includes('/chat/'):
      route.placeholders['jss-main'] = [chat];
      break;
    case item.includes('/minimal-header/'):
      route.placeholders = {
        'jss-navigationbar': [getMinimalNavBar()],
        'jss-notifications': [],
        'jss-main': [],
        'jss-meta': [cookieWall(), metaData(), structuredDataSchema(), vwo(), hotjar()],
        'jss-footer': [getFooter()],
      };

      route.placeholders['jss-main'] = [];
      break;
    case item.includes('/protected-page/'):
      if (route.fields) {
        route.fields.enableAuthentication.value = true;
      }
      route.placeholders['jss-main'] = [chat];
      break;
    case item.includes('/slimme-thuisbatterij-hero/'):
      route.placeholders['jss-main'] = [slimmeThuisbatterij];
      break;

    case item.includes('/slimme-thuisbatterij/'):
      route.placeholders['jss-main'] = [slimmeThuisbatterijFields];
      break;

    default:
      // Current "404" response from Sitecore:
      return {
        sitecore: {
          context: {
            // eslint-disable-next-line no-loss-of-precision
            visitorIdentificationTimestamp: 637781993280964784,
            pageEditing: false,
            site: { name: 'website_eneco_be' },
            pageState: 'normal',
            language: 'nl-BE',
          },
          route: null,
        },
      } as unknown as LayoutServiceData; // todo: not sure yet if Sitecore is wrong or the jss type is wrong; but this is literally the Sitecore response on a 404
  }
  return {
    sitecore: {
      context,
      route,
    },
  };
};

export default mock;
