import { LayoutServiceData, RouteData } from '@sitecore/types/lib';

import advancePaymentAmountForm from '../../../apps/advancepayment/nl/multilabel/advancePaymentAmountForm';
import advancePaymentAmount from '../../../apps/advancepayment/nl/oxxio/advancePaymentAmount';
import cookieWall from '../../../apps/cookiewall/nl/shared/cookieWall';
import dashboard from '../../../apps/dashboard/nl/multilabel/dashboard';
import dashboardOffboarding from '../../../apps/dashboard/nl/multilabel/dashboardOffboarding';
import invoiceDetail from '../../../apps/invoices/shared/multilabel/invoiceDetail.nl';
import outstandingInvoices from '../../../apps/invoices/shared/multilabel/outstandingInvoices.nl';
import overviewInvoices from '../../../apps/invoices/shared/multilabel/overviewInvoices.nl';
import partialPaymentInvoices from '../../../apps/invoices/shared/multilabel/partialPaymentInvoices.nl';
import payAllInvoices from '../../../apps/invoices/shared/multilabel/payAllInvoices.nl';
import orders from '../../../apps/orders/nl/multilabel/orders';
import ordersDashboard from '../../../apps/orders/nl/multilabel/ordersDashboard';
import PaymentTransactionStatus from '../../../apps/payment/shared/multilabel/PaymentTransactionStatus';
import paymentArrangement from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangement';
import paymentArrangementConfirmStep from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementConfirmStep';
import paymentArrangementEndStep from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementEndStep';
import paymentArrangementHeader from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementHeader';
import paymentArrangementInvoicesStep from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementInvoicesStep';
import paymentArrangementStartDateStep from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementStartDateStep';
import paymentArrangementTermsStep from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementTermsStep';
import paymentArrangementTransactionV2 from '../../../apps/paymentarrangement/nl/oxxio/PaymentArrangementTransaction';
import productsNavigation from '../../../apps/products/nl/multilabel/v2/productsnavigation';
import products from '../../../apps/products/nl/multilabel/v2/productsV2';
import consentSettings from '../../../apps/profile/nl/multilabel/consentSettings';
import consentSettingsForm from '../../../apps/profile/nl/multilabel/consentSettingsForm';
import contactPreferencesForm from '../../../apps/profile/nl/multilabel/contactPreferencesForm';
import correspondenceAddress from '../../../apps/profile/nl/multilabel/correspondenceAddress';
import correspondenceAddressForm from '../../../apps/profile/nl/multilabel/correspondenceAddressForm';
import gdprCard from '../../../apps/profile/nl/multilabel/gdprCard';
import homeProfile from '../../../apps/profile/nl/multilabel/homeProfile';
import homeProfileForm from '../../../apps/profile/nl/multilabel/homeProfileForm';
import ismaSettings from '../../../apps/profile/nl/multilabel/ismaSettings';
import ismaSettingsForm from '../../../apps/profile/nl/multilabel/ismaSettingsForm';
import monthlyEnergyReport from '../../../apps/profile/nl/multilabel/monthlyEnergyReport';
import paymentPlan from '../../../apps/profile/nl/multilabel/paymentPlan';
import paymentPlanForm from '../../../apps/profile/nl/multilabel/paymentPlanForm';
import personalDetailsForm from '../../../apps/profile/nl/multilabel/personalDetailsForm';
import smartMeterSettings from '../../../apps/profile/nl/multilabel/smartMeterSettings';
import smartMeterSettingsForm from '../../../apps/profile/nl/multilabel/smartMeterSettingsForm';
import passwordDetails from '../../../apps/profile/shared/multilabel/passwordDetailsCard';
import passwordDetailsForm from '../../../apps/profile/shared/multilabel/passwordDetailsForm';
import personalDetails from '../../../apps/profile/shared/multilabel/personalDetails';
import usernameDetails from '../../../apps/profile/shared/multilabel/usernameDetailsCard';
import usernameDetailsForm from '../../../apps/profile/shared/multilabel/usernameDetailsForm';
import {
  getMoveAbroadFlow,
  getMoveElderlyHomeFlow,
} from '../../../apps/relocation/nl/multilabel/moveOutFlow/moveOutFlow';
import relocationConfirmationStep from '../../../apps/relocation/nl/multilabel/relocationConfirmationStep';
import relocationContactDetailsStep from '../../../apps/relocation/nl/multilabel/relocationContactDetailsStep';
import relocationDisallowed from '../../../apps/relocation/nl/multilabel/relocationDisallowed';
import relocationHeader from '../../../apps/relocation/nl/multilabel/relocationHeader';
import relocationMoveInCard from '../../../apps/relocation/nl/multilabel/relocationMoveInCard';
import relocationMoveInForm from '../../../apps/relocation/nl/multilabel/relocationMoveInForm';
import relocationMoveInStep from '../../../apps/relocation/nl/multilabel/relocationMoveInStep';
import relocationMoveOutCard from '../../../apps/relocation/nl/multilabel/relocationMoveOutCard';
import relocationMoveOutForm from '../../../apps/relocation/nl/multilabel/relocationMoveOutForm';
import relocationMoveOutStep from '../../../apps/relocation/nl/multilabel/relocationMoveOutStep';
import relocationNewAddressStep from '../../../apps/relocation/nl/multilabel/relocationNewAddressStep';
import relocationProcessing from '../../../apps/relocation/nl/multilabel/relocationProcessing';
import relocationProductsCard from '../../../apps/relocation/nl/multilabel/relocationProductsCard';
import usageV1 from '../../../apps/usage/nl/multilabel/v1/usage';
import usageCapYearnoteView from '../../../apps/usagecap/shared/multilabel/yearnoteView';
import { getBaseContext } from '../../../common';
import mandate from '../../../components/nl/multilabel/mandate';
import invoicesNLSubMenu from '../../../components/nl/multilabel/SideNavigation/invoices';
import profileSubMenu from '../../../components/nl/multilabel/SideNavigation/profile';
import chat from '../../../components/shared/chat';
import faq from '../../../components/shared/faq';
import GTM from '../../../components/shared/GTM';
import { getNavBar } from '../../../components/shared/navigationBar';
import notificationsBar from '../../../components/shared/notificationsBar';
import { Mock } from '../../../types/mock';

const setPageTitle = (route: RouteData, title: string) => {
  if (!route.fields) route.fields = {};
  route.fields['pageTitle'] = { value: title };
  route.fields['browserTitle'] = { value: `${title} | Mijn Oxxio` };
};

const matchesRoute = (targetRoute: string, currentRoute: string) => {
  return targetRoute === currentRoute || `${targetRoute}/` === currentRoute;
};

const mock: Mock = (item = '/', options = { locale: 'nl-NL', site: 'nl-oxxio-selfservice' }) => {
  const chatFeature = [chat()];

  const basePath = '/mijn-oxxio';
  const context = getBaseContext({ domain: 'oxxio', basePath: item, ...options });
  const NOGRID_LAYOUT_ID = '2dfd92c7-5914-4d46-a1ad-7733cfdd9c70';
  const route: LayoutServiceData['sitecore']['route'] = {
    name: 'Mijn Oxxio',
    displayName: 'Mijn Oxxio',
    databaseName: 'web',
    fields: {
      allowFrontEndRouting: { value: false },
      pageTitle: { value: 'Default Page Title from Mock' },
      browserTitle: { value: 'Default browser Title from Mock' },
    },
    placeholders: {
      'jss-navigationbar': [
        getNavBar({
          includeSubHeader: true,
          includeTopHeader: false,
          label: 'oxxio',
          businessUnit: 'nl',
          currentRoute: item,
        }),
      ],
      'jss-notifications': [notificationsBar()],
      'jss-main': [],
      'jss-footer': [],
      'jss-meta': [mandate(), GTM(), cookieWall()],
      'jss-left': [],
    },
  };

  switch (true) {
    case item.startsWith(`${basePath}/termijnbedrag/aanpassen`):
      setPageTitle(route, 'Termijnbedrag aanpassen');
      route.placeholders['jss-main'] = [advancePaymentAmountForm(basePath), faq('exampleFAQ'), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/termijnbedrag`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [advancePaymentAmount(basePath), ...chatFeature];
      route.placeholders['jss-main'] = [faq('exampleFAQ')];
      break;

    case item.startsWith(`${basePath}/profiel/accountgegevens/gebruikersnaam-wijzigen`):
      setPageTitle(route, 'Gebruikersnaam');
      route.placeholders['jss-main'] = [usernameDetailsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/accountgegevens/wachtwoord-wijzigen`):
      setPageTitle(route, 'Wachtwoord');
      route.placeholders['jss-main'] = [passwordDetailsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/accountgegevens`):
      setPageTitle(route, 'Accountgegevens');
      route.placeholders['jss-main'] = [usernameDetails(basePath), passwordDetails(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/betaalgegevens/betaalwijze`):
      setPageTitle(route, 'Betaalgegevens wijzigen');
      route.placeholders['jss-main'] = [paymentPlanForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/betaalgegevens`):
      setPageTitle(route, 'Betaalgegevens');
      route.placeholders['jss-main'] = [paymentPlan(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/energierapport`):
      setPageTitle(route, 'Energierapport');
      route.placeholders['jss-main'] = [monthlyEnergyReport(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/woonprofiel/wijzigen`):
      setPageTitle(route, 'Woonprofiel wijzigen');
      route.placeholders['jss-main'] = [homeProfileForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/woonprofiel`):
      setPageTitle(route, 'Woonprofiel');
      route.placeholders['jss-main'] = [homeProfile(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/privacy/slimme-meterinstellingen-wijzigen/`):
      setPageTitle(route, 'Je slimme-meterinstellingen');
      route.placeholders['jss-main'] = [smartMeterSettingsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/privacy/gegevensgebruik-wijzigen/`):
      setPageTitle(route, 'Gebruik van je gegevens');
      route.placeholders['jss-main'] = [consentSettingsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/privacy/slimmer-inkopen-wijzigen/`):
      setPageTitle(route, 'Slimmer inkopen');
      route.placeholders['jss-main'] = [ismaSettingsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/privacy`):
      setPageTitle(route, 'Privacy');
      route.placeholders['jss-main'] = [
        smartMeterSettings(basePath),
        consentSettings(basePath),
        ismaSettings(basePath),
        gdprCard(basePath),
        ...chatFeature,
      ];
      break;

    case item.startsWith(`${basePath}/profiel/contactvoorkeur`):
      setPageTitle(route, 'Contactvoorkeuren');
      route.placeholders['jss-main'] = [contactPreferencesForm, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/mijn-gegevens/correspondentieadres`):
      setPageTitle(route, 'Correspondentieadres');
      route.placeholders['jss-main'] = [correspondenceAddressForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/mijn-gegevens/persoonsgegevens`):
      setPageTitle(route, 'Persoonsgegevens');
      route.placeholders['jss-main'] = [personalDetailsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/mijn-gegevens`):
      setPageTitle(route, 'Mijn gegevens');
      route.placeholders['jss-main'] = [personalDetails(basePath), correspondenceAddress(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/producten`):
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };

      setPageTitle(route, 'Producten');
      context.basePath = `${basePath}/producten/`;
      route.placeholders['jss-left'] = [productsNavigation];
      route.placeholders['jss-main'] = [products, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/termijnen/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [paymentArrangementHeader, paymentArrangementTermsStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/startdatum/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [
        paymentArrangementHeader,
        paymentArrangementStartDateStep,
      ];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/bevestig/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [
        paymentArrangementHeader,
        paymentArrangementConfirmStep,
      ];

      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [
        paymentArrangementHeader,
        paymentArrangementInvoicesStep,
      ];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/alles-betalen/`):
      setPageTitle(route, "Alle nota's betalen");
      route.placeholders['jss-main'] = [payAllInvoices(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/gedeelte-betalen/`):
      setPageTitle(route, 'Gedeelte betalen');
      route.placeholders['jss-main'] = [partialPaymentInvoices(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/bedankt/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [paymentArrangementHeader, paymentArrangementEndStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/betalen/`):
      setPageTitle(route, 'Deel betalen');
      route.placeholders['jss-main'] = [paymentArrangementTransactionV2, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling`):
      setPageTitle(route, 'Betalingsregeling');
      route.placeholders['jss-main'] = [paymentArrangement, faq('betalingsregeling'), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/nota`):
      setPageTitle(route, 'Nota betalen');
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/nota/`;
      route.placeholders['jss-main'] = [invoiceDetail(basePath)];
      break;

    case item.startsWith(`${basePath}/notas/openstaand`):
      setPageTitle(route, "Openstaande nota's");
      route.placeholders['jss-main'] = [
        PaymentTransactionStatus,
        outstandingInvoices(basePath),
        faq('exampleFAQ'),
        ...chatFeature,
      ];
      break;

    case item.startsWith(`${basePath}/notas/overzicht`):
      setPageTitle(route, "Overzicht nota's");
      route.placeholders['jss-main'] = [overviewInvoices(basePath), ...chatFeature];

      break;

    case item.startsWith(`${basePath}/verhuizen/bevestigen`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationConfirmationStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/contactgegevens`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationContactDetailsStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/oude-adres`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationMoveOutStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/ontvangst-sleutels`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationMoveInStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/verhuizing-doorgegeven`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationDisallowed];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationNewAddressStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizing/einddatum-levering`):
      setPageTitle(route, 'Wanneer lever je de sleutel van je oude adres in?');
      route.placeholders['jss-main'] = [relocationMoveOutForm, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/verhuizing/startdatum-levering`):
      setPageTitle(route, 'Nieuw adres');
      route.placeholders['jss-main'] = [relocationMoveInForm, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/verhuizing/`):
      setPageTitle(route, 'Verhuisdossier');
      route.placeholders['jss-main'] = [
        relocationProcessing,
        relocationMoveOutCard,
        relocationMoveInCard,
        relocationProductsCard,
        ...chatFeature,
      ];
      break;
    case item.startsWith(`${basePath}/verbruik/verbruiksplafond`):
      route.layoutId = NOGRID_LAYOUT_ID;

      route.placeholders['jss-no-grid'] = [
        {
          uid: 'b07311eb-366e-4724-bed3-e2ab56606e91',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: '',
          },
          placeholders: {
            'jss-section-main': [
              {
                uid: '89eef173-d180-4841-9fa9-7e0b6354765c',
                componentName: 'TitleTextCTA',
                dataSource: '{92d54cce-c312-465d-8b3f-4588d4784718}',
                params: {},
                fields: {
                  title: {
                    value: 'Verbruiksplafond overzicht',
                  },
                  content: {
                    value:
                      'Hier zie je hoe je verbruik ervoor staat ten opzichte van het verbruiksplafond dat geldt tot en met december 2023.',
                  },

                  link: {
                    value: {
                      href: '/mijn-oxxio/verbruik/',
                      text: 'Naar de pagina Verbruik',
                      linktype: 'internal',
                      target: '',
                    },
                  },
                },
              },
              usageCapYearnoteView,
            ],
          },
        },

        {
          uid: 'd425ad28-eaff-4a74-9305-87219c25c170',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: 'backgroundPrimary',
          },
          placeholders: {
            'jss-section-main': [
              {
                componentName: 'SplitView',
                params: {
                  columnArrangement: '8-4',
                },
                placeholders: {
                  'jss-split-view-first': [faq('exampleFAQ', 'Veelgestelde Vragen')],
                  'jss-split-view-second': [],
                },
              },
            ],
          },
        },

        ...chatFeature,
      ];
      route.placeholders['jss-main'] = [];

      break;

    case item.startsWith(`${basePath}/verbruik`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [usageV1];
      break;

    case item.startsWith(`${basePath}/offboarding`):
      // use DC_CUSTOMER_ID = ********
      setPageTitle(route, '');
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [dashboardOffboarding(basePath), ...chatFeature];
      break;

    /* Track & Trace */
    case item.startsWith(`${basePath}/bestellingen`):
      // Orders has its own router, uses allowFrontEndRouting routing which means sitecore
      // will always sent the basePath as `${basePath}/bestellingen`
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };

      context.basePath = `${basePath}/bestellingen/`;
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [orders];
      route.placeholders['jss-main'] = [...chatFeature];

      break;

    // MOVEOUT FLOW
    case item.startsWith(`${basePath}/verhuizen-buitenland/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-move-out-flow'] = getMoveAbroadFlow(item, basePath, '/verhuizen-buitenland');
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
        flowName: { value: 'movingAbroad' },
      };
      context.basePath = `${basePath}/verhuizen-buitenland/`;
      break;

    // MOVEOUT FLOW
    case item.startsWith(`${basePath}/verhuizen-verzorgingstehuis/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-move-out-flow'] = getMoveElderlyHomeFlow(
        item,
        basePath,
        '/verhuizen-verzorgingstehuis',
      );
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
        flowName: { value: 'movingCareHome' },
      };
      context.basePath = `${basePath}/verhuizen-verzorgingstehuis/`;
      break;

    case matchesRoute(`${basePath}/start/persoonsgegevens/wijzigen`, item):
      setPageTitle(route, 'Persoonsgegevens wijzigen');
      route.placeholders['jss-main'] = [personalDetailsForm(basePath, '/start/persoonsgegevens/'), ...chatFeature];

      break;

    case matchesRoute(`${basePath}/start/persoonsgegevens`, item):
      setPageTitle(route, 'Persoonsgegevens');
      route.placeholders['jss-main'] = [personalDetails(basePath, '/start/persoonsgegevens/wijzigen'), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/start`, item):
      route.layoutId = NOGRID_LAYOUT_ID;
      setPageTitle(route, '');
      route.placeholders['jss-no-grid'] = [ordersDashboard];
      route.placeholders['jss-meta'] = [...chatFeature];
      break;

    case matchesRoute(`${basePath}`, item):
      setPageTitle(route, 'Mijn oxxio');
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [dashboard(basePath)];
      route.placeholders['jss-meta'] = [...chatFeature];
      break;

    default:
      route.placeholders['jss-main'] = [];
      break;
  }

  // profiel or producten specific
  if (item.startsWith(`${basePath}/profiel`)) {
    route.placeholders['jss-left'] = [profileSubMenu(basePath, item)];
  } else if (
    item.startsWith(`${basePath}/notas`) &&
    !item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/`)
  ) {
    route.placeholders['jss-left'] = [invoicesNLSubMenu(basePath, item)];
  } else {
    route.placeholders['jss-left'] = [];
  }

  return {
    sitecore: {
      context,
      route,
    },
  };
};

export default mock;
