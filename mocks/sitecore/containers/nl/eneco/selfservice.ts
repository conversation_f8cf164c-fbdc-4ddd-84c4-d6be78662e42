import { LayoutServiceData, RouteData } from '@sitecore/types/lib';

import advancePaymentAmount from '../../../apps/advancepayment/nl/eneco/advancePaymentAmount';
import advancePaymentAmountForm from '../../../apps/advancepayment/nl/multilabel/advancePaymentAmountForm';
import cookieWall from '../../../apps/cookiewall/nl/shared/cookieWall';
import dashboard from '../../../apps/dashboard/nl/multilabel/dashboard';
import dashboardOffboarding from '../../../apps/dashboard/nl/multilabel/dashboardOffboarding';
import dynamicPricing from '../../../apps/dynamicPricing/nl/multilabel/dynamicPricing';
import invoiceDetail from '../../../apps/invoices/shared/multilabel/invoiceDetail.nl';
import invoicesAccountCard from '../../../apps/invoices/shared/multilabel/invoicesAccountCard';
import latestYearNoteDownloadCard from '../../../apps/invoices/shared/multilabel/latestYearNoteDownloadCard.nl';
import outstandingInvoices from '../../../apps/invoices/shared/multilabel/outstandingInvoices.nl';
import overviewInvoices from '../../../apps/invoices/shared/multilabel/overviewInvoices.nl';
import partialPaymentInvoices from '../../../apps/invoices/shared/multilabel/partialPaymentInvoices.nl';
import payAllInvoices from '../../../apps/invoices/shared/multilabel/payAllInvoices.nl';
import maintenance from '../../../apps/maintenance/nl/multilabel/maintenance.nl';
import orders from '../../../apps/orders/nl/multilabel/orders';
import ordersDashboard from '../../../apps/orders/nl/multilabel/ordersDashboard';
import PaymentTransactionStatus from '../../../apps/payment/shared/multilabel/PaymentTransactionStatus';
import paymentArrangement from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangement';
import paymentArrangementAccountCard from '../../../apps/paymentarrangement/nl/eneco/paymentArrangementAccountCard';
import paymentArrangementConfirmStep from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangementConfirmStep';
import paymentArrangementHeader from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangementHeader';
import paymentArrangementInvoicesStep from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangementInvoicesStep';
import paymentArrangementStartDateStep from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangementStartDateStep';
import paymentArrangementTermsStep from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangementTermsStep';
import paymentArrangementTransactionV2 from '../../../apps/paymentarrangement/nl/eneco/PaymentArrangementTransaction';
import downloadContracts from '../../../apps/products/nl/multilabel/v2/downloadContracts';
import productsNavigation from '../../../apps/products/nl/multilabel/v2/productsnavigation';
import productsV2 from '../../../apps/products/nl/multilabel/v2/productsV2';
import consentSettings from '../../../apps/profile/nl/multilabel/consentSettings';
import consentSettingsForm from '../../../apps/profile/nl/multilabel/consentSettingsForm';
import contactPreferencesForm from '../../../apps/profile/nl/multilabel/contactPreferencesForm';
import correspondenceAddress from '../../../apps/profile/nl/multilabel/correspondenceAddress';
import correspondenceAddressForm from '../../../apps/profile/nl/multilabel/correspondenceAddressForm';
import {
  dynamicPricingIntervalData,
  dynamicPricingIntervalDataDetails,
} from '../../../apps/profile/nl/multilabel/dynamicPricingIntervalData';
import { dynamicPricingIsma, dynamicPricingIsmaDetails } from '../../../apps/profile/nl/multilabel/dynamicPricingIsma';
import gdprCard from '../../../apps/profile/nl/multilabel/gdprCard';
import homeProfile from '../../../apps/profile/nl/multilabel/homeProfile';
import homeProfileAccountCard from '../../../apps/profile/nl/multilabel/homeProfileAccountCard';
import homeProfileForm from '../../../apps/profile/nl/multilabel/homeProfileForm';
import ismaSettings from '../../../apps/profile/nl/multilabel/ismaSettings';
import ismaSettingsForm from '../../../apps/profile/nl/multilabel/ismaSettingsForm';
import monthlyEnergyReport from '../../../apps/profile/nl/multilabel/monthlyEnergyReport';
import monthlyEnergyReportAccountCard from '../../../apps/profile/nl/multilabel/monthlyEnergyReportAccountCard';
import paymentPlan from '../../../apps/profile/nl/multilabel/paymentPlan';
import paymentPlanForm from '../../../apps/profile/nl/multilabel/paymentPlanForm';
import personalDetailsForm from '../../../apps/profile/nl/multilabel/personalDetailsForm';
import privacyAccountCard from '../../../apps/profile/nl/multilabel/privacyAccountCard';
import smartMeterSettings from '../../../apps/profile/nl/multilabel/smartMeterSettings';
import smartMeterSettingsForm from '../../../apps/profile/nl/multilabel/smartMeterSettingsForm';
import passwordDetails from '../../../apps/profile/shared/multilabel/passwordDetailsCard';
import passwordDetailsForm from '../../../apps/profile/shared/multilabel/passwordDetailsForm';
import personalDetails from '../../../apps/profile/shared/multilabel/personalDetails';
import usernameDetails from '../../../apps/profile/shared/multilabel/usernameDetailsCard';
import usernameDetailsForm from '../../../apps/profile/shared/multilabel/usernameDetailsForm';
import {
  getMoveAbroadFlow,
  getMoveElderlyHomeFlow,
} from '../../../apps/relocation/nl/multilabel/moveOutFlow/moveOutFlow';
import relocationConfirmationStep from '../../../apps/relocation/nl/multilabel/relocationConfirmationStep';
import relocationContactDetailsStep from '../../../apps/relocation/nl/multilabel/relocationContactDetailsStep';
import relocationDisallowed from '../../../apps/relocation/nl/multilabel/relocationDisallowed';
import relocationHeader from '../../../apps/relocation/nl/multilabel/relocationHeader';
import relocationMoveInCard from '../../../apps/relocation/nl/multilabel/relocationMoveInCard';
import relocationMoveInForm from '../../../apps/relocation/nl/multilabel/relocationMoveInForm';
import relocationMoveInStep from '../../../apps/relocation/nl/multilabel/relocationMoveInStep';
import relocationMoveOutCard from '../../../apps/relocation/nl/multilabel/relocationMoveOutCard';
import relocationMoveOutForm from '../../../apps/relocation/nl/multilabel/relocationMoveOutForm';
import relocationMoveOutStep from '../../../apps/relocation/nl/multilabel/relocationMoveOutStep';
import relocationNewAddressStep from '../../../apps/relocation/nl/multilabel/relocationNewAddressStep';
import relocationProcessing from '../../../apps/relocation/nl/multilabel/relocationProcessing';
import relocationProductsCard from '../../../apps/relocation/nl/multilabel/relocationProductsCard';
import usageV1 from '../../../apps/usage/nl/multilabel/v1/usage';
import usageV2 from '../../../apps/usage/nl/multilabel/v2/usage';
import usageCapMonthView from '../../../apps/usagecap/shared/multilabel/monthView';
import usageCapYearnoteView from '../../../apps/usagecap/shared/multilabel/yearnoteView';
import { getBaseContext } from '../../../common';
import mandate from '../../../components/nl/multilabel/mandate';
import { myEnvDisabledLandingPageCard } from '../../../components/nl/multilabel/MyEnvDisabledLandingPageCard/MyEnvDisabledLandingPageCard';
import { relocationQuickLinks } from '../../../components/nl/multilabel/QuickLinks/relocationQuickLinks';
import invoicesNLSubMenu from '../../../components/nl/multilabel/SideNavigation/invoices';
import profileSubMenu from '../../../components/nl/multilabel/SideNavigation/profile';
import chat from '../../../components/shared/chat';
import faq from '../../../components/shared/faq';
import GTM from '../../../components/shared/GTM';
import { getNavBar } from '../../../components/shared/navigationBar';
import notificationBox from '../../../components/shared/notificationBox';
import notificationsBar from '../../../components/shared/notificationsBar';
import notasQuickLinks from '../../../components/shared/quickLinks';
import productsSideNav from '../../../components/shared/SideNavigation/products';
import { Mock } from '../../../types/mock';

const matchesRoute = (targetRoute: string, currentRoute: string) => {
  return targetRoute === currentRoute || `${targetRoute}/` === currentRoute;
};

const setPageTitle = (route: RouteData, title: string) => {
  if (!route.fields) route.fields = {};
  route.fields['pageTitle'] = { value: title };
  route.fields['browserTitle'] = { value: `${title} | Mijn Eneco` };
};

const mock: Mock = (item = '/', options = { locale: 'nl-NL', site: 'nl-eneco-selfservice' }) => {
  const context = getBaseContext({
    domain: 'eneco',
    basePath: item,
    locale: options.locale,
    siteName: options.site,
    customerId: options.customerId,
  });

  const chatFeature = [chat()];
  const basePath = '/mijn-eneco';
  const NOGRID_LAYOUT_ID = '2dfd92c7-5914-4d46-a1ad-7733cfdd9c70';
  const LANDING_PAGE_LAYOUT_ID = '********-D6D3-446B-82F1-888D78C3F4D3';

  const route: LayoutServiceData['sitecore']['route'] = {
    name: 'Mijn Eneco',
    displayName: 'Mijn Eneco',
    databaseName: 'web',
    fields: {
      allowFrontEndRouting: { value: false },
      pageTitle: { value: 'Default Page Title from Mock' },
      browserTitle: { value: 'Default browser Title from Mock' },
    },
    placeholders: {
      'jss-navigationbar': [
        getNavBar({
          includeSubHeader: true,
          includeTopHeader: false,
          label: 'eneco',
          businessUnit: 'nl',
          currentRoute: item,
        }),
      ],
      'jss-notifications': [notificationsBar()],
      'jss-main': [],
      'jss-main-bottom': [],
      'jss-footer': [],
      'jss-meta': [mandate(), GTM(), cookieWall()],
      'jss-left': [],
      'jss-right': [],
    },
  };

  switch (true) {
    case item.startsWith(`${basePath}/profiel/accountgegevens/gebruikersnaam-wijzigen`):
      setPageTitle(route, 'Gebruikersnaam');
      route.placeholders['jss-main'] = [usernameDetailsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/accountgegevens/wachtwoord-wijzigen`):
      setPageTitle(route, 'Wachtwoord');
      route.placeholders['jss-main'] = [passwordDetailsForm(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/accountgegevens`):
      setPageTitle(route, 'Accountgegevens');
      route.placeholders['jss-main'] = [usernameDetails(basePath), passwordDetails(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/profiel/betaalgegevens/betaalwijze`):
      setPageTitle(route, 'Betaalgegevens wijzigen');
      route.placeholders['jss-main'] = [paymentPlanForm(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/betaalgegevens`, item):
      setPageTitle(route, 'Betaalgegevens');
      route.placeholders['jss-main'] = [paymentPlan(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/energierapport`, item):
      setPageTitle(route, 'Energierapport');
      route.placeholders['jss-main'] = [monthlyEnergyReportAccountCard, monthlyEnergyReport(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/woonprofiel/wijzigen`, item):
      setPageTitle(route, 'Woonprofiel wijzigen');
      route.placeholders['jss-main'] = [homeProfileForm(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/woonprofiel`, item):
      setPageTitle(route, 'Woonprofiel');
      route.placeholders['jss-main'] = [homeProfileAccountCard, homeProfile(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/privacy/slimme-meterinstellingen-wijzigen/`, item):
      setPageTitle(route, 'Je slimme-meterinstellingen');
      route.placeholders['jss-main'] = [smartMeterSettingsForm(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/privacy/gegevensgebruik-wijzigen/`, item):
      setPageTitle(route, 'Gebruik van je gegevens');
      route.placeholders['jss-main'] = [consentSettingsForm(basePath)];
      break;

    case matchesRoute(`${basePath}/profiel/privacy/slimmer-inkopen-wijzigen/`, item):
      setPageTitle(route, 'Slimmer inkopen');
      route.placeholders['jss-main'] = [ismaSettingsForm(basePath)];
      break;

    case matchesRoute(`${basePath}/profiel/privacy/interval-gegevens-bekijken/`, item):
      setPageTitle(route, 'Slimme meter');
      route.placeholders['jss-main'] = [dynamicPricingIntervalDataDetails(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/privacy/isma-bekijken`, item):
      setPageTitle(route, 'iSma');
      route.placeholders['jss-main'] = [dynamicPricingIsmaDetails(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/privacy`, item):
      setPageTitle(route, 'Privacy');
      route.placeholders['jss-main'] = [
        privacyAccountCard,
        dynamicPricingIntervalData(basePath),
        dynamicPricingIsma(basePath),
        smartMeterSettings(basePath),
        consentSettings(basePath),
        ismaSettings(basePath),
        gdprCard(basePath),
        ...chatFeature,
      ];
      break;

    case matchesRoute(`${basePath}/profiel/contactvoorkeuren`, item):
      setPageTitle(route, 'Contactvoorkeuren');
      route.placeholders['jss-main'] = [contactPreferencesForm, ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/mijn-gegevens/correspondentieadres`, item):
      setPageTitle(route, 'Correspondentieadres');
      route.placeholders['jss-main'] = [correspondenceAddressForm(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/mijn-gegevens/persoonsgegevens`, item):
      setPageTitle(route, 'Persoonsgegevens');
      route.placeholders['jss-main'] = [personalDetailsForm(basePath), ...chatFeature];
      break;

    case matchesRoute(`${basePath}/profiel/mijn-gegevens`, item):
      setPageTitle(route, 'Mijn gegevens');
      route.placeholders['jss-main'] = [personalDetails(basePath), correspondenceAddress(basePath), ...chatFeature];
      route.placeholders['jss-right'] = [relocationQuickLinks(basePath, '/profiel/mijn-gegevens')];
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/termijnen/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [paymentArrangementHeader, paymentArrangementTermsStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/startdatum/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [
        paymentArrangementHeader,
        paymentArrangementStartDateStep,
      ];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/bevestig/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [
        paymentArrangementHeader,
        paymentArrangementConfirmStep,
      ];

      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-payment-arrangement-flow'] = [
        paymentArrangementHeader,
        paymentArrangementInvoicesStep,
      ];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/betalingsregeling/aanvragen/`;
      break;

    case item.startsWith(`${basePath}/notas/alles-betalen/`):
      setPageTitle(route, "Alle nota's betalen");
      route.placeholders['jss-main'] = [payAllInvoices(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/gedeelte-betalen/`):
      setPageTitle(route, 'Gedeelte betalen');
      route.placeholders['jss-main'] = [partialPaymentInvoices(basePath), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling/betalen`):
      setPageTitle(route, 'Termijn betalen');
      route.placeholders['jss-main'] = [paymentArrangementTransactionV2, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/notas/betalingsregeling`):
      setPageTitle(route, 'Betalingsregeling');
      route.placeholders['jss-main'] = [
        paymentArrangementAccountCard,
        PaymentTransactionStatus,
        paymentArrangement,
        notificationBox,
        faq('betalingsregeling'),
        ...chatFeature,
      ];
      break;

    case item.startsWith(`${basePath}/notas/nota/`):
      setPageTitle(route, 'Nota betalen');
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/notas/nota/`;
      route.placeholders['jss-main'] = [invoiceDetail(basePath)];
      break;

    case item.startsWith(`${basePath}/notas/openstaand`):
      setPageTitle(route, "Openstaande nota's");
      route.placeholders['jss-main'] = [
        PaymentTransactionStatus,
        outstandingInvoices(basePath),
        faq('exampleFAQ'),
        ...chatFeature,
      ];
      break;

    case item.startsWith(`${basePath}/notas`):
      setPageTitle(route, "Overzicht nota's");
      route.placeholders['jss-main'] = [invoicesAccountCard, outstandingInvoices(basePath), overviewInvoices(basePath)];
      route.placeholders['jss-main-bottom'] = [faq('exampleFAQ'), ...chatFeature];
      route.placeholders['jss-right'] = [latestYearNoteDownloadCard, notasQuickLinks];

      break;

    case item.startsWith(`${basePath}/onderhoud`):
      // Maintenance has its own router, uses allowFrontEndRouting routing which means sitecore
      // will always send the basePath as `${basePath}/onderhoud`

      //This page has a dynamic page title
      setPageTitle(route, '');
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/onderhoud/`;
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [maintenance(basePath)];
      route.placeholders['jss-main'] = [faq('exampleFAQ'), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/producten`):
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };

      setPageTitle(route, 'Producten');
      context.basePath = `${basePath}/producten/`;
      route.placeholders['jss-main'] = [productsV2, ...chatFeature];
      route.placeholders['jss-right'] = [downloadContracts, relocationQuickLinks(basePath, '/producten')];
      break;

    case item.startsWith(`${basePath}/termijnbedrag/aanpassen`):
      setPageTitle(route, 'Termijnbedrag aanpassen');
      route.placeholders['jss-main'] = [advancePaymentAmountForm(basePath), faq('exampleFAQ'), ...chatFeature];
      break;

    case item.startsWith(`${basePath}/termijnbedrag`):
      setPageTitle(route, 'Termijnbedrag');
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [advancePaymentAmount(basePath)];
      route.placeholders['jss-main'] = [faq('exampleFAQ'), ...chatFeature];
      route.placeholders['jss-left'] = [productsSideNav(basePath, item)];
      break;

    case item.startsWith(`${basePath}/uurtarieven`):
      route.layoutId = NOGRID_LAYOUT_ID;
      context.restricted = true;
      setPageTitle(route, 'Uurtarieven');
      route.placeholders['jss-no-grid'] = [
        dynamicPricing(),
        {
          uid: 'd425ad28-eaff-4a74-9305-87219c25c170',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: 'backgroundPrimary',
          },
          placeholders: {
            'jss-section-main': [
              {
                componentName: 'SplitView',
                params: {
                  columnArrangement: '8-4',
                },
                placeholders: {
                  'jss-split-view-first': [faq('exampleFAQ', 'Veelgestelde Vragen')],
                  'jss-split-view-second': [],
                },
              },
            ],
          },
        },
        ...chatFeature,
      ];
      break;

    case item.startsWith(`${basePath}/bestellingen`):
      // Orders has its own router, uses allowFrontEndRouting routing which means sitecore
      // will always sent the basePath as `${basePath}/bestellingen`

      //This page has a dynamic page title
      setPageTitle(route, '');
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };

      context.basePath = `${basePath}/bestellingen/`;
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [orders];
      route.placeholders['jss-main'] = [...chatFeature];

      break;

    // MOVEOUT FLOW
    case item.startsWith(`${basePath}/verhuizen-buitenland/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-move-out-flow'] = getMoveAbroadFlow(item, basePath, '/verhuizen-buitenland');
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
        flowName: { value: 'movingAbroad' },
      };
      context.basePath = `${basePath}/verhuizen-buitenland/`;
      break;

    // MOVEOUT FLOW
    case item.startsWith(`${basePath}/verhuizen-verzorgingstehuis/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-move-out-flow'] = getMoveElderlyHomeFlow(
        item,
        basePath,
        '/verhuizen-verzorgingstehuis',
      );
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
        flowName: { value: 'movingCareHome' },
      };
      context.basePath = `${basePath}/verhuizen-verzorgingstehuis/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/bevestigen`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationConfirmationStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/contactgegevens`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationContactDetailsStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/oude-adres`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationMoveOutStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/ontvangst-sleutels`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationMoveInStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/verhuizing-doorgegeven`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationDisallowed];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizen/`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-header'] = [];
      route.placeholders['jss-app-relocation-flow'] = [relocationHeader, relocationNewAddressStep];
      route.fields = {
        ...route.fields,
        allowFrontEndRouting: { value: true },
      };
      context.basePath = `${basePath}/verhuizen/`;
      break;

    case item.startsWith(`${basePath}/verhuizing/einddatum-levering`):
      setPageTitle(route, 'Wanneer lever je de sleutel van je oude adres in?');
      route.placeholders['jss-main'] = [relocationMoveOutForm, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/verhuizing/startdatum-levering`):
      setPageTitle(route, 'Nieuw adres');
      route.placeholders['jss-main'] = [relocationMoveInForm, ...chatFeature];
      break;

    case item.startsWith(`${basePath}/verhuizing/`):
      setPageTitle(route, 'Verhuisdossier');
      route.placeholders['jss-main'] = [
        relocationProcessing,
        relocationMoveOutCard,
        relocationMoveInCard,
        relocationProductsCard,
        ...chatFeature,
      ];
      break;

    case item.startsWith(`${basePath}/verbruik/verbruiksplafond`):
      route.layoutId = NOGRID_LAYOUT_ID;

      route.placeholders['jss-no-grid'] = [
        {
          uid: 'b07311eb-366e-4724-bed3-e2ab56606e91',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: '',
          },
          placeholders: {
            'jss-section-main': [
              {
                uid: '89eef173-d180-4841-9fa9-7e0b6354765c',
                componentName: 'TitleTextCTA',
                dataSource: '{92d54cce-c312-465d-8b3f-4588d4784718}',
                params: {},
                fields: {
                  title: {
                    value: 'Verbruiksplafond overzicht',
                  },
                  content: {
                    value:
                      'Hier zie je hoe je verbruik ervoor staat ten opzichte van het verbruiksplafond dat geldt tot en met december 2023.',
                  },

                  link: {
                    value: {
                      href: '/mijn-eneco/verbruik/',
                      text: 'Naar de pagina Verbruik',
                      linktype: 'internal',
                      target: '',
                    },
                  },
                },
              },
              usageCapMonthView,
              usageCapYearnoteView,
            ],
          },
        },

        {
          uid: 'd425ad28-eaff-4a74-9305-87219c25c170',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: 'backgroundPrimary',
          },
          placeholders: {
            'jss-section-main': [
              {
                componentName: 'SplitView',
                params: {
                  columnArrangement: '8-4',
                },
                placeholders: {
                  'jss-split-view-first': [faq('exampleFAQ', 'Veelgestelde Vragen')],
                  'jss-split-view-second': [],
                },
              },
            ],
          },
        },

        ...chatFeature,
      ];
      route.placeholders['jss-main'] = [];

      break;

    case item.startsWith(`${basePath}/verbruik-dynamisch`):
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [
        usageV2,
        ...chatFeature,
        {
          uid: 'd425ad28-eaff-4a74-9305-87219c25c170',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: 'backgroundPrimary',
          },
          placeholders: {
            'jss-section-main': [
              {
                componentName: 'SplitView',
                params: {
                  columnArrangement: '8-4',
                },
                placeholders: {
                  'jss-split-view-first': [faq('exampleFAQ', 'Veelgestelde Vragen')],
                  'jss-split-view-second': [],
                },
              },
            ],
          },
        },
      ];
      route.placeholders['jss-main'] = [];

      break;

    case item.startsWith(`${basePath}/verbruik`):
      setPageTitle(route, 'Verbruik');
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [
        usageV1,
        ...chatFeature,
        {
          uid: 'd425ad28-eaff-4a74-9305-87219c25c170',
          componentName: 'Section',
          dataSource: '',
          params: {
            backgroundColor: 'backgroundPrimary',
          },
          placeholders: {
            'jss-section-main': [
              {
                componentName: 'SplitView',
                params: {
                  columnArrangement: '8-4',
                },
                placeholders: {
                  'jss-split-view-first': [faq('exampleFAQ', 'Veelgestelde Vragen')],
                  'jss-split-view-second': [],
                },
              },
            ],
          },
        },
      ];
      route.placeholders['jss-main'] = [];

      break;

    case matchesRoute(`${basePath}/klantenservice`, item):
      route.placeholders['jss-main'] = [];
      break;

    case item.startsWith(`${basePath}/offboarding`):
      // use DC_CUSTOMER_ID = 38110376
      setPageTitle(route, '');
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [dashboardOffboarding(basePath), ...chatFeature];
      break;

    // Dashboard for onboarding customers to be displayed on '/mijn-eneco'
    case matchesRoute(`${basePath}/start`, item):
      route.layoutId = NOGRID_LAYOUT_ID;
      setPageTitle(route, '');
      route.placeholders['jss-no-grid'] = [ordersDashboard];
      route.placeholders['jss-meta'] = [...chatFeature];
      break;

    case matchesRoute(`${basePath}`, item):
      setPageTitle(route, 'Mijn eneco');
      route.layoutId = NOGRID_LAYOUT_ID;
      route.placeholders['jss-no-grid'] = [dashboard(basePath)];
      route.placeholders['jss-meta'] = [...chatFeature];
      break;

    case matchesRoute(`${basePath}/geblokkeerd`, item):
      setPageTitle(route, '');
      route.layoutId = LANDING_PAGE_LAYOUT_ID;
      route.fields = {
        ...route.fields,
        backgroundImage: {
          value: {
            alt: '',
            formats: [
              {
                format: 'default',
                src: 'https://www.acc.eneco.nl/-/media/eneco-nl/lifestyle/ks_eneco_lifestyle_03-04-25_0707.jpg?sc_lang=nl-nl&cx=0.72&cy=0.43&cw=2430&ch=1621&hash=E6E71A4F89960196A65DECE08D618492',
                height: '1621px',
                width: '2430px',
              },
              {
                format: 'mobile',
                src: 'https://www.acc.eneco.nl/-/media/eneco-nl/lifestyle/ks_eneco_lifestyle_03-04-25_0707.jpg?sc_lang=nl-nl&cx=0.72&cy=0.43&cw=1080&ch=720&hash=12523D513927A716FCDB7EC10DAE53F8',
                height: '720px',
                width: '1080px',
              },
            ],
          },
        },
      };
      route.placeholders['jss-meta'] = [...chatFeature];
      route.placeholders['jss-navigationbar'] = [
        getNavBar({
          includeSubHeader: false,
          includeTopHeader: false,
          label: 'eneco',
          businessUnit: 'nl',
          currentRoute: item,
        }),
      ];
      route.placeholders['jss-no-grid'] = [myEnvDisabledLandingPageCard()];

      break;

    default:
      // Current "404" response from Sitecore:
      return {
        sitecore: {
          context: {
            // eslint-disable-next-line no-loss-of-precision
            visitorIdentificationTimestamp: 637781993280964784,
            pageEditing: false,
            site: { name: 'website_eneco_main' },
            pageState: 'normal',
            language: 'nl-NL',
          },
          route: null,
        },
      } as unknown as LayoutServiceData; // todo: not sure yet if Sitecore is wrong or the jss type is wrong; but this is literally the Sitecore response on a 404
  }

  // profiel or producten specific
  if (item.startsWith(`${basePath}/profiel`)) {
    route.placeholders['jss-left'] = [profileSubMenu(basePath, item)];
  } else if (item.startsWith(`${basePath}/producten/v1`)) {
    route.placeholders['jss-left'] = [];
  } else if (item.startsWith(`${basePath}/producten`)) {
    route.placeholders['jss-left'] = [productsNavigation];
  } else if (
    item.startsWith(`${basePath}/notas`) &&
    !item.startsWith(`${basePath}/notas/betalingsregeling/aanvragen/`)
  ) {
    route.placeholders['jss-left'] = [invoicesNLSubMenu(basePath, item)];
  } else {
    route.placeholders['jss-left'] = [];
  }

  return {
    sitecore: {
      context,
      route,
    },
  };
};

export default mock;
