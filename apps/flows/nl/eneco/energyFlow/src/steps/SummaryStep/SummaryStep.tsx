import React, { FC, useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { isValidIBAN, friendlyFormatIBAN } from 'ibantools';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { DATE_FORMAT_ISO, parse } from '@common/date';
import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import useDC from '@dc/useDC';
import { Overview } from '@eneco/flows';
import { Header, Layout, NextButton, Price } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useMachine as useMachineUpdate } from '@eneco-packages/xstate-custom/xstate-react';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { SummaryStepRendering } from '@sitecore/types/SummaryStep';
import { Box, Bleed, Heading, InputText, Stack, Text, TextLink, NotificationBox } from '@sparky';

import { Ribbon, PriceDetailsOverview, TermsAndConditions, Error } from '../../components';
import { FlowConsumerContextEnergy } from '../../context';
import {
  getOffer,
  getProductTitleFromProductType,
  getProductIconTypeFromProductType,
  getCashbackTexts,
  getOfferTermsAndConditions,
  checkOfferIncludesDynamicPricingProduct,
  checkOfferIncludesNonDynamicPricingProduct,
} from '../../helpers';
import { ENECO_ENERGY_FLOW_GIFT } from '../../helpers/constants';
import { checkOffersIncludeVariablePricingProduct } from '../../helpers/misc';
import { useFlowTracking } from '../../hooks';
import { useFlowNavigation } from '../../hooks/updateContextAndGoToNextStep';
import { useOfferMachine } from '../../hooks/useOfferMachine';
import { sendOrderMachine } from '../../machines/sendOrder/sendOrder.machine';

interface FormFields {
  iban?: string;
}

export const SummaryStep: FC = () => {
  const { businessUnit, label } = useDC();
  const { useFlowSelector, useFlowActorRef } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const sendToFlowMachine = useFlowActorRef().send;
  const { updateContextAndGoToNextStep } = useFlowNavigation();
  const [orderState, sendToOrderMachine] = useMachineUpdate(sendOrderMachine, {
    input: { businessUnit: businessUnit, label: label },
  });

  const {
    context: flowContext,
    context: {
      initials,
      firstName,
      surnamePreposition,
      surname,
      birthdate,
      postalCode,
      houseNumber,
      houseNumberSuffix,
      street,
      city,
      isMoving,
      iban,
      phoneNumber,
      emailAddress,
      isSME,
      isPersonalDetailsEditProhibited,
    },
  } = flowState;

  const { SummaryStep: { fields: textualData } = {} } = usePlaceholderContent<{
    SummaryStep: SummaryStepRendering;
  }>();

  const { offerState, sendToOfferMachine } = useOfferMachine(businessUnit, label);

  const { context: { offer } = {} } = offerState;

  const formSchema = yup.object({
    ...(!isPersonalDetailsEditProhibited && {
      iban: yup
        .string()
        .required(textualData?.paymentDetails?.ibanFormField?.value?.requiredMessage)
        .test(
          'iban validation',
          textualData?.paymentDetails?.ibanFormField?.value?.validationMessage ?? '',
          iban => !!(iban && isValidIBAN(iban.replace(/\s+/g, '').toUpperCase())),
        ),
    }),
  });

  const { trackPurchase, trackFillUsage } = useFlowTracking();
  const { date, currency, format, address } = useFormatter();

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isFailed, setIsFailed] = useState(false);

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);
  const [isNotAuthenticated, setIsNotAuthenticated] = useState(false);

  const [isOrderFailed, setIsOrderFailed] = useState(false);

  const selectedOffer = getOffer(flowContext, offer);
  const selectedOfferTermsAndConditions = getOfferTermsAndConditions(flowContext, offer);
  const selectedCashbackTexts = getCashbackTexts(
    selectedOffer?.costDiscounts?.cashback?.type,
    textualData?.promotionTexts,
  );

  const shouldShowDynamicPricingContractNotification =
    !checkOffersIncludeVariablePricingProduct(selectedOffer) &&
    checkOfferIncludesDynamicPricingProduct(selectedOffer) &&
    !checkOfferIncludesNonDynamicPricingProduct(selectedOffer) &&
    !!textualData?.notifications?.dynamicPricingContractNotification?.value?.content;

  const shouldShowHybridContractNotification =
    !checkOffersIncludeVariablePricingProduct(selectedOffer) &&
    checkOfferIncludesDynamicPricingProduct(selectedOffer) &&
    checkOfferIncludesNonDynamicPricingProduct(selectedOffer) &&
    !!textualData?.notifications?.hybridContractNotification?.value?.content;

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  const {
    handleSubmit,
    getValues,
    register,
    formState: { errors, isSubmitSuccessful },
  } = useForm<FormFields>({
    mode: 'onSubmit',
    defaultValues: { iban },
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    sendToOfferMachine({
      type: 'GET_OFFER',
      flowContext: { ...flowContext },
    });
  }, []);

  useEffect(() => {
    if (isSubmitSuccessful) {
      sendToOrderMachine({
        type: 'SEND_ORDER',
        values: { ...flowContext, ...getValues() },
        offers: offerState.context.offer ?? { isMkb: !!isSME },
      });
    }
  }, [isSubmitSuccessful]);

  useEffect(() => {
    setIsOrderFailed(orderState.matches('ERROR'));

    if (orderState.matches('SUCCESS')) {
      if (selectedOffer) trackPurchase(selectedOffer, offer, orderState.context.switchTypes);
      trackFillUsage({ ...flowContext });

      updateContextAndGoToNextStep({ ...getValues() });
    }

    if (orderState.matches('ERROR')) {
      setIsSuccessful(false);
    }
  }, [orderState]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('FETCHING'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
      setIsNotAuthenticated(offerState.matches('ERROR_NOT_AUTHENTICATED'));
    }
  }, [offerState]);

  return (
    <Layout.Content variant="B" isLoading={isLoading} handleSubmit={handleSubmit}>
      {isSuccessful && (
        <>
          <Header>
            <Header.Title>{textualData?.content?.title?.value}</Header.Title>
            {textualData?.content?.text?.value && <Header.Text>{textualData.content.text.value}</Header.Text>}
          </Header>

          {textualData?.content?.notification?.value?.content && (
            <NotificationBox
              isAlert={false}
              variant={textualData?.content?.notification?.value?.variant}
              title={textualData?.content?.notification?.value?.title}
              text={<RichText html={textualData?.content?.notification?.value?.content} />}
            />
          )}

          {shouldShowDynamicPricingContractNotification && (
            <TrackedNotificationBox
              isAlert={false}
              label={textualData?.notifications?.dynamicPricingContractNotification?.value?.title}
              title={textualData?.notifications?.dynamicPricingContractNotification?.value?.title ?? ''}
              text={<RichText html={textualData?.notifications?.dynamicPricingContractNotification?.value?.content} />}
              variant={textualData?.notifications?.dynamicPricingContractNotification?.value?.variant}
            />
          )}

          {shouldShowHybridContractNotification && (
            <TrackedNotificationBox
              isAlert={false}
              label={textualData?.notifications?.hybridContractNotification?.value?.title}
              title={textualData?.notifications?.hybridContractNotification?.value?.title ?? ''}
              text={<RichText html={textualData?.notifications?.hybridContractNotification?.value?.content} />}
              variant={textualData?.notifications?.hybridContractNotification?.value?.variant}
            />
          )}

          <Stack.Item>
            <Overview>
              <Overview.Header headingLevel="h2">{textualData?.personalDetailsOverview?.title?.value}</Overview.Header>

              <Overview.Details
                type="profile"
                title={textualData?.personalDetailsOverview?.personalDetailsLabel?.value ?? ''}
                editButton={
                  isPersonalDetailsEditProhibited
                    ? undefined
                    : {
                        label: textualData?.personalDetailsOverview?.editDetailsLabel?.value ?? '',
                        onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_PERSONAL_DETAILS' }),
                      }
                }>
                <Text as="p">
                  {textualData?.personalDetailsOverview?.nameLabel?.value}:{' '}
                  {`${firstName ? firstName : initials} ${
                    surnamePreposition ? surnamePreposition + ' ' : ''
                  }${surname}`}
                </Text>
                {birthdate && (
                  <Text as="p">
                    {textualData?.personalDetailsOverview?.birthDateLabel?.value}: {birthdate}
                  </Text>
                )}

                <Text as="p">
                  {textualData?.personalDetailsOverview?.phoneNumberLabel?.value}: {phoneNumber}
                </Text>
                <Text as="p">
                  {textualData?.personalDetailsOverview?.emailAddressLabel?.value}: {emailAddress}
                </Text>
              </Overview.Details>

              <Overview.Details
                type="address"
                title={textualData?.personalDetailsOverview?.addressDetailsLabel?.value ?? ''}
                editButton={
                  isPersonalDetailsEditProhibited
                    ? undefined
                    : {
                        label: textualData?.personalDetailsOverview?.editDetailsLabel?.value ?? '',
                        onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_ADDRESS' }),
                      }
                }>
                <Text as="p">{address.streetAddress({ houseNumber, houseNumberSuffix, street })}</Text>
                <Text as="p">{address.postalCodeCity({ postalCode, city })}</Text>
              </Overview.Details>

              {!isPersonalDetailsEditProhibited && (
                <Overview.Details
                  type="situation"
                  title={textualData?.personalDetailsOverview?.situationDetailsLabel?.value ?? ''}
                  editButton={{
                    label: textualData?.personalDetailsOverview?.editDetailsLabel?.value ?? '',
                    onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_MOVING' }),
                  }}>
                  <Text as="p">
                    {isMoving
                      ? textualData?.personalDetailsOverview?.willBeMovingLabel?.value
                      : textualData?.personalDetailsOverview?.wontBeMovingLabel?.value}
                  </Text>
                </Overview.Details>
              )}
            </Overview>
          </Stack.Item>

          <Stack.Item>
            <Overview>
              <Overview.Header>{textualData?.productsOverview?.title?.value}</Overview.Header>

              {selectedCashbackTexts && (
                <Bleed top="6">
                  <Ribbon
                    size="large"
                    emphasis="medium"
                    title={format(selectedCashbackTexts.title, {
                      [`${selectedOffer?.costDiscounts?.cashback?.type}`]: currency.euroNoFractionDigits(
                        selectedOffer?.costDiscounts?.cashback?.value ?? 0,
                      ),
                    })}
                    text={selectedCashbackTexts?.text}
                    trigger={selectedCashbackTexts?.trigger}
                  />
                </Bleed>
              )}

              {selectedOffer?.products.map(product => (
                <Overview.Product
                  key={product.type}
                  type={getProductIconTypeFromProductType(product.type)}
                  title={getProductTitleFromProductType(product.type, textualData?.productTypeLabels)}
                  subtitle={product.description}
                  costsMonthly={{
                    value: product?.costsMonthly?.[costsType] ?? 0,
                    period: textualData?.costDetails?.perMonthLabel?.value,
                  }}
                  costsYearly={{
                    value: product?.costsYearly?.[costsType] ?? 0,
                    period: textualData?.costDetails?.perYearLabel?.value,
                  }}
                />
              ))}

              <Overview.ProductSummary
                label={textualData?.costDetails?.totalCostsLabel?.value}
                costs={{
                  value: selectedOffer?.costsMonthly?.[costsType] ?? 0,
                  period: textualData?.costDetails?.perMonthLabel?.value,
                }}
                costDetailsTrigger={
                  <Text size="BodyS">
                    <TrackedDialog
                      description={<RichText html={textualData?.productsOverview?.costDetailsDialog?.value?.content} />}
                      title={textualData?.productsOverview?.costDetailsDialog?.value?.title}
                      trigger={
                        <TextLink>{textualData?.productsOverview?.costDetailsDialog?.value?.triggerText}</TextLink>
                      }>
                      <PriceDetailsOverview offer={selectedOffer} flowContext={flowContext} textualData={textualData} />
                    </TrackedDialog>
                  </Text>
                }>
                <Box paddingTop="2">
                  <Text size="BodyS" align="right">
                    {/* whenever the "costsYearlyPromotion" or the "cashback" value is defined 
                      we should render different text label and different price */}
                    <Price
                      suffix={textualData?.costDetails?.[
                        selectedOffer?.costsYearlyPromotion || selectedOffer?.costDiscounts?.cashback?.value
                          ? 'totalCostsFirstYearLabel'
                          : 'perYearLabel'
                      ]?.value?.toLowerCase()}
                      emphasis="low">
                      {selectedOffer?.costsYearlyPromotion?.[costsType] ??
                        Number(selectedOffer?.costsYearly?.[costsType] ?? 0) -
                          (selectedOffer?.costDiscounts?.cashback?.type === ENECO_ENERGY_FLOW_GIFT
                            ? 0
                            : Number(selectedOffer?.costDiscounts?.cashback?.value ?? 0))}
                    </Price>
                  </Text>
                </Box>
              </Overview.ProductSummary>

              <Overview.Footer>
                <Text size="BodyS" color="textLowEmphasis">
                  <Stack direction="column" gap="1">
                    <Stack direction="row" gap="1" alignX="justify">
                      <Text>{textualData?.productsOverview?.contractStartDateLabel?.value}</Text>
                      <Text>
                        {selectedOffer?.contractStartDate &&
                          date.short(parse(selectedOffer.contractStartDate, DATE_FORMAT_ISO))}
                      </Text>
                    </Stack>

                    {selectedOffer?.contractEndDate && (
                      <Stack direction="row" gap="1" alignX="justify">
                        <Text>{textualData?.productsOverview?.contractEndDateLabel?.value}</Text>
                        <Text>{date.short(parse(selectedOffer.contractEndDate, DATE_FORMAT_ISO))}</Text>
                      </Stack>
                    )}
                  </Stack>
                </Text>
              </Overview.Footer>
            </Overview>
          </Stack.Item>

          {!isPersonalDetailsEditProhibited && (
            <>
              <Stack.Item>
                <Stack direction="column" gap="4">
                  <Heading as="h3" size="XS">
                    {textualData?.paymentDetails?.title?.value}
                  </Heading>
                  <Text>{textualData?.paymentDetails?.description?.value}</Text>
                </Stack>
              </Stack.Item>

              <Stack.Item>
                <InputText
                  {...register('iban', {
                    onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                      const target = event.target;

                      const caretPosition = target.selectionStart;

                      const currentValue = target.value;
                      const newValue = friendlyFormatIBAN(currentValue) ?? '';

                      target.value = newValue;
                      target.selectionEnd =
                        currentValue.length !== newValue.length ? (caretPosition ?? 0) + 1 : caretPosition;
                    },
                  })}
                  label={textualData?.paymentDetails?.ibanFormField?.value?.label ?? ''}
                  hint={textualData?.paymentDetails?.ibanFormField?.value?.hint}
                  placeholder={textualData?.paymentDetails?.ibanFormField?.value?.placeholder}
                  error={errors.iban?.message}
                />

                <Box paddingTop="3">
                  <Text size="BodyS" color="textLowEmphasis">
                    <TrackedDialog
                      title={textualData?.paymentDetails?.ibanDialog?.value?.title}
                      trigger={<TextLink>{textualData?.paymentDetails?.ibanDialog?.value?.triggerText}</TextLink>}>
                      <RichText html={textualData?.paymentDetails?.ibanDialog?.value?.content} />
                    </TrackedDialog>
                  </Text>
                </Box>
              </Stack.Item>
            </>
          )}

          <TermsAndConditions
            label={label}
            flowContext={flowContext}
            textualData={textualData}
            offer={selectedOffer}
            offerTermsAndConditions={selectedOfferTermsAndConditions}
          />

          <NextButton isLoading={isSubmitSuccessful}>{textualData?.content?.nextStepText?.value}</NextButton>
        </>
      )}

      {(isFailed || isOrderFailed) && (
        <Error
          type={isNotAuthenticated ? 'AUTHENTIFICATION' : 'GENERIC'}
          textualData={{
            notification: isNotAuthenticated
              ? textualData?.genericError?.errorNotAuthenticatedNotification
              : isNotAvailable
                ? textualData?.genericError?.errorNotAvailableNotification
                : isNotFound
                  ? textualData?.genericError?.errorNotFoundNotification
                  : isOrderFailed
                    ? textualData?.genericError?.sendOrderErrorNotification
                    : textualData?.genericError?.fetchOfferErrorNotification,
            buttonLabel: isNotAuthenticated
              ? textualData?.genericError?.loginButtonText
              : !isNotAvailable && !isNotFound
                ? textualData?.genericError?.tryAgainButtonText
                : undefined,
          }}
        />
      )}
    </Layout.Content>
  );
};
