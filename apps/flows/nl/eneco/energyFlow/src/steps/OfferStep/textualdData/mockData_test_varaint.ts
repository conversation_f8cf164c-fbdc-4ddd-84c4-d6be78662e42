import { OverviewStepRendering } from '@sitecore/types/OverviewStep';

export const mockData: OverviewStepRendering = {
  uid: 'e95fed1f-a32c-43c8-8b18-b83ffd34bbb2',
  componentName: 'OverviewStep',
  dataSource: '{AE0ADD81-C266-439E-BA3D-9CFAFF9F4748}',
  params: {},
  fields: {
    content: {
      priceIncludingText: {
        value: 'Prijzen incl. 21% btw, overheidsheffingen en netbeheerkosten.',
      },
      promotionPricePluralLabel: {
        value: 'Na {discountDuration} maanden: {monthlyCosts}',
      },
      promotionPriceSingleLabel: {
        value: 'Na {discountDuration} maand: {monthlyCosts}',
      },
      text: { value: '' },
      title: { value: 'Je aanbod' },
      totalPriceLabel: { value: 'Totaal eerste jaar: {yearlyCosts}' },
      usp1Content: { value: '<p>De&nbsp;<strong>overstap</strong> regelen we voor je</p>' },
      usp2Content: { value: '<p>Je komt <strong>nooit zonder energie</strong> te zitten</p>' },
      usp3Content: { value: '<p><strong>14 dagen</strong> bedenktijd</p>' },
      nextStepText: { value: 'Naar je gegevens' },
      moreInfoDialog: {
        value: {
          title: 'Prijsopbouw',
          content: '',
          triggerText: 'Bekijk je tarieven',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      descriptionContent: {
        value: '',
      },
      priceDetailsDialog: {
        value: {
          title: 'Prijsopbouw',
          content: '',
          triggerText: 'Bekijk je tarieven',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      timeOfUseInfoDialog: {
        value: {
          title: 'ToU dialog',
          content:
            "<p>Bij Eneco kies je afhankelijk van je adres en type woning voor een vast contract, een dynamisch contract of een combinatie daarvan.&nbsp;<br />\n<br />\n<strong><br />\nVast energiecontract</strong><br />\nJe betaalt een vast tarief voor je stroom en gas. Je hoeft je dus geen zorgen te maken over stijgende stroom- of gasprijzen. Een vast contract heeft een opzegtermijn van 30 dagen.&nbsp;<br />\n<br />\n<strong>Dynamisch energiecontract<br />\n</strong>Je betaalt actuele uurprijzen voor stroom en dagprijzen voor gas. Dit is een contract voor onbepaalde tijd. Je kunt dus altijd boeteloos opzeggen met een opzegtermijn van negen dagen. Vind jij het fijn om je energieverbruik in te plannen? In de Eneco-app vind je altijd de goedkoopste momenten. Dit geeft flexibiliteit. Maar er zit ook risico aan verbonden. Als gas- en stroomprijzen opeens stijgen kan je flink meer betalen.<br />\n<br />\n<strong>Combinatiecontract</strong><br />\nDit is nieuw! Je kiest bijvoorbeeld om je gasprijs 1 of 3 jaar vast te zetten. En betaalt voor stroom altijd de actuele uurprijs. Dan hoef je je geen zorgen te maken over stijgende gasprijzen, en kun je je stroomverbruik slim inplannen met behulp van de Eneco-app. Het dynamische stroomcontract kun je opzeggen met een opzegtermijn van 9 dagen, het vaste gascontract met een opzegtermijn van 30 dagen.<br />\n<br />\n<strong>Hoe werkt het met maand- en jaarnota's?</strong><br />\nDat werkt exact zoals je gewend bent. Je betaalt gewoon een termijnbedrag per maand. En dat termijnbedrag is gebaseerd op je historisch verbruik en de verwachte energieprijzen van het komende jaar. Het verschil tussen de energie die je hebt verbruikt en je betaalde termijnbedragen verwerken we op je jaarnota.</p>",
          triggerText: 'Welk type contracten kan je kiezen',
          submitButtonText: '',
          cancelButtonText: '',
        },
      },
      timeOfUseInfoDialogUspList: {
        value: {
          enum: [
            {
              name: 'usp_1',
              value: 'usp_1',
              label: 'Je hebt een een contract met een vaste looptijd en vaste leveringstarieven voor een jaar.',
            },
            {
              name: 'usp_2',
              value: 'usp_2',
              label:
                'Elke dag heb je twee vaste voordeelmomenten waarin je 30% korting krijgt op het standaardtarief. ',
            },
            {
              name: 'usp_3',
              value: 'usp_3',
              label: 'De voordeelmomenten zijn elke dag tussen 10:00 uur - 17:00 uur en tussen 22:00 uur - 5:00 uur ',
            },
          ],
        },
      },
      notification: { value: { variant: 'info', title: '', content: '' } },
    },
    costDetails: {
      totalCostsLabel: { value: 'Totaal' },
      text: { value: '' },
      title: { value: 'Prijsopbouw' },
      variableCostsLabel: { value: 'Variabele kosten' },
      fixedCostsLabel: { value: 'Vaste kosten' },
      perMonthLabel: { value: 'per maand' },
      perYearLabel: { value: 'per jaar' },
      monthlyTermLabel: { value: 'Maandbedrag' },
      yearlyTermLabel: { value: 'Jaarbedrag' },
      totalCostsFirstYearLabel: { value: 'Eerste jaar' },
      dynamicPricingNotification: {
        value: {
          variant: 'info',
          title: 'Overweging bij een dynamisch energiecontract',
          content:
            'Als de prijzen op de energiemarkt stijgen, stijgen jouw leveringstarieven bij een dynamisch energiecontract direct mee. Dat is een risico. Klik&nbsp;<a href="https://www.eneco.nl/duurzame-energie/dynamisch-energiecontract/" class="overlay-source">hier</a>&nbsp;<a rel="noopener noreferrer" href="https://www.eneco.nl/duurzame-energie/dynamisch-energiecontract/" target="_blank"></a>voor meer informatie.',
        },
      },
      usageLabel: { value: 'Verbruik' },
      hybridNotification: {
        value: {
          variant: 'info',
          title: 'Overweging bij een dynamisch stroomcontract',
          content:
            'Als de prijzen op de energiemarkt stijgen, stijgt jouw leveringstarief bij een dynamisch stroomcontract direct mee. Dat is een risico. Klik&nbsp;<a href="https://www.eneco.nl/duurzame-energie/dynamisch-energiecontract/" class="overlay-source">hier</a>&nbsp;<a rel="noopener noreferrer" href="https://www.eneco.nl/duurzame-energie/dynamisch-energiecontract/" target="_blank"></a>voor meer informatie.',
        },
      },
      variableProductNotification: {
        value: {
          variant: 'info',
          title: 'Overweging bij een variabel contract',
          content:
            'Bij een variabel contract kunnen je tarieven meerdere keren per jaar stijgen of dalen. Dat betekent dat de prijs soms heel hoog kan worden, zoals tijdens de energiecrisis. Dit kan grote financiële gevolgen hebben. Als je tarieven wijzigen, laten we je dit minstens 30 dagen van tevoren weten. Je kunt altijd kosteloos je contract opzeggen. Ook als je het niet eens bent met de hoogte van je nieuwe tarieven.',
        },
      },
    },
    costDetailsExplanation: {
      monthlyFeesHybridParagraph: {
        value: {
          title: 'Maandbedrag en actuele tarieven',
          content:
            '<p>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re. In dit prijsoverzicht staan altijd:</p>\n<ul>\n    <li> de actuele verwachte tarieven voor stroom. De getoonde leveringstarieven voor het dynamische stroomcontract gelden voor de situatie in je woning op basis van het aantal (actieve) telwerken. De tarieven voor elektriciteit vari&euml;ren per uur. Je vindt de actuele tarieven een dag van tevoren (rond 15.00 uur) terug in de Eneco app.&nbsp;</li>\n</ul>\n<ul>\n    <li>de actuele tarieven voor het vaste contract voor gas. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd</li>\n</ul>\n<br />\n<p><strong>Verwachte leveringskosten</strong></p>\n<p><span style="background-color: #ffffff; color: #000000;">De leveringskosten zijn de verwachte leveringskosten en zijn inclusief de inkoopvergoeding van &euro; 0,02923 per kWh tot en met 31 juli 2025 (&euro; 0,02398 vanaf 1 augustus 2025) voor stroom. </span></p>',
        },
      },
      redeliveryHybridParagraph: {
        value: {
          title: 'Teruglevering',
          content:
            'Als je zelf stroom opwekt - en dit niet direct zelf gebruikt - lever je deze opgewekte stroom terug. Dit kan alleen als je aangemeld bent op&nbsp;<a rel="noopener noreferrer" href="https://energieleveren.nl/" target="_blank">energieleveren.nl</a>. Je ontvangt voor teruggeleverde stroom het werkelijke uurtarief per kWh op het moment van terugleveren, inclusief de inkoopvergoeding en overheidsheffingen en btw. Lever je over het gehele verbruiksjaar, waar de (jaar)nota op betrekking heeft, meer terug dan dat je verbruikt, dan ontvang je, in afwijking van artikel 3 van het Voorwaardenoverzicht, voor het netto teruggeleverde volume de marktprijs per kWh, die in het uur van teruglevering geldt (exclusief de inkoopvergoeding en overheidsheffingen en btw) min de verkoopvergoeding (<span style="background-color: #ffffff; color: #000000;">&euro; 0,02923 per kWh tot en met 31 juli 2025 (&euro; 0,02398 vanaf 1 augustus 2025).</span>&nbsp;De verkoopvergoeding is ter dekking van de kosten die Eneco maakt voor de verkoop en afhandeling van stroom op de energiemarkt en kan wijzigen, waardoor de vergoeding voor netto teruggeleverde stroom kan fluctueren.',
        },
      },
      monthlyFeesDynamicPricingProductParagraph: {
        value: {
          title: 'Maandbedrag en actuele tarieven',
          content:
            '<p>Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. De getoonde leveringstarieven gelden voor de situatie in je woning op basis van het aantal (actieve) telwerken. De tarieven voor elektriciteit vari&euml;ren per uur. Je vindt de actuele tarieven een dag van tevoren (rond 15.00 uur) terug in de Eneco app. In dit prijsoverzicht staan altijd de actuele verwachte tarieven.&nbsp;<span style="background: white; line-height: 107%; color: black;">Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re.</span></p>\n<br />\n<p><span style="background: white; line-height: 107%; color: black;"><strong>Verwachte leveringskosten</strong></span></p>\n<p><span style="background: white; line-height: 107%; color: black;">De leveringskosten zijn de verwachte leveringskosten en zijn inclusief de inkoopvergoeding van <span style="background-color: #ffffff; color: #000000;"><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">&euro; 0,</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">02923</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> per kWh tot en met 31 juli 2025 </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">(</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">&euro; 0,</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">02398</span></span><span data-contrast="none" class="TextRun SCXW200749613 BCX0" style="color: #000000; background-color: #ffffff; margin: 0px; padding: 0px; line-height: 18.3458px;"><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> vanaf 1 augustus 2025)</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> vo</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">o</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">r</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">s</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">t</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">r</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">o</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">o</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">m</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">.</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> Heb je ook g</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">as</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">? De Inkoopvergoeding daarvoor </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">i</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">s</span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;"> </span><span class="NormalTextRun SCXW200749613 BCX0" style="margin: 0px; padding: 0px;">&euro; 0,08246 per m3 tot en met 31 juli 2025, &euro; 0,08289 vanaf 1 augustus 2025.</span></span></span></span></p>',
        },
      },
      monthlyFeesVariablePricingProductParagraph: {
        value: {
          title: 'Maandbedrag en actuele tarieven',
          content:
            'Je betaalt voor stroom een leveringstarief per kilowattuur (kWh). Dit tarief kan door ons maandelijks, per kwartaal of per (half)jaar worden aangepast. De keuze om het tarief te wijzigen en hoe vaak dit gebeurt, maakt Eneco. Als er iets verandert, laten we je dat altijd minimaal 30 dagen van tevoren weten met een persoonlijk bericht.',
        },
      },
      contractEndDateParagraph: {
        value: {
          title: 'Verwachte einddatum',
          content:
            'De verwachte einddatum contract is een indicatie gebaseerd op de gemiddelde doorlooptijd van een aanmelding en de gekozen looptijd.',
        },
      },
      currentRatesParagraph: {
        value: {
          title: '',
          content: '',
        },
      },
      deliveryCostsParagraph: {
        value: {
          title: 'Netbeheerkosten',
          content:
            'Netbeheerkosten zijn de kosten voor je aansluiting en voor het transport door de netbeheerder. Het bedrag wordt door ons namens de netbeheerder bij je in rekening gebracht.',
        },
      },
      explanationTitle: {
        value: 'Toelichting prijsopbouw',
      },
      fixedCostsParagraph: {
        value: {
          title: 'Vaste leveringskosten',
          content:
            'Vaste leveringskosten zijn kosten waarbij het niet uitmaakt hoeveel energie je verbruikt. Eerder heette dit vastrecht.&nbsp;',
        },
      },
      governmentAndLeviesParagraph: {
        value: {
          title: 'Overheidsheffingen en belastingen',
          content:
            'Met overheidsheffingen bedoelen we de energiebelasting. De leveringstarieven van stroom en gas zijn inclusief energiebelasting. De overheid heft per kWh stroom en m&sup3; gas energiebelasting om verbruikers te stimuleren zuinig en bewust met energie om te gaan.<br />\n&nbsp;<br />\nOver de leveringstarieven, overheidsheffingen en energiebelasting, en over de netbeheerkosten, betaal je btw. De energiebelasting en btw dragen wij af aan de Belastingdienst.&nbsp;',
        },
      },
      governmentLeviesParagraph: {
        value: {
          title: '',
          content: '',
        },
      },
      monthlyFeesParagraph: {
        value: {
          title: 'Maandbedrag en actuele tarieven',
          content:
            'In dit prijsoverzicht staan altijd de actuele tarieven. In de toekomst kunnen deze tarieven wijzigen. Als dit zo is, dan informeren wij je natuurlijk op tijd. Het verbruik dat je hebt ingevuld gebruiken we voor deze berekening. Het maandbedrag berekenen we door de jaarlijkse kosten door twaalf te delen. Dat kan afwijken van het termijnbedrag dat je gaat betalen. Omdat daarvoor het historisch verbruik van je woning wordt gebruikt. In de Eneco app of op Mijn Eneco pas je later je termijnbedrag makkelijk aan. Alle gebruikte prijzen in deze berekening zijn op basis van een aansluitwaarde van maximaal 3 x 25 Amp&egrave;re.',
        },
      },
      priceDifferencesParagraph: {
        value: {
          title: 'Prijsverschillen',
          content:
            'Bij het berekenen van je kosten ronden wij de tarieven af. Soms ontstaan hierdoor kleine afrondingsverschillen op het prijsoverzicht en de energienota.',
        },
      },
      residenceFunctionParagraph: {
        value: {
          title: 'Verblijfsfunctie',
          content:
            'Alleen als er sprake is van een woon-, werk- of andere verblijfsfunctie, heb je recht op vermindering energiebelasting. Dit is een door de overheid vastgesteld kortingbedrag op de energiebelasting.',
        },
      },
      variableCostsParagraph: {
        value: {
          title: 'Variabele kosten',
          content:
            'De getoonde variabele kosten zijn op basis van de door jou opgegeven hoeveelheid verbruik van kWh / m&sup3; / GJ.',
        },
      },
      redeliveryParagraph: {
        value: {
          title: 'Lever je terug uit zonnepanelen?',
          content:
            '<p><strong>Terugleverkosten&nbsp;</strong><br />\nAls je stroom opwekt, verbruiken je apparaten in huis ook meteen een deel van deze opgewekte stroom. De stroom die je zelf niet verbruikt, lever je terug aan het elektriciteitsnet. Hierover betaal je per kWh terugleverkosten (exclusief overheidsheffingen en inclusief btw).&nbsp;&nbsp;<br />\n&nbsp;<br />\n<strong>Salderen&nbsp;&nbsp;</strong><br />\nOp je jaarnota verminderen we je jaarverbruik met wat je teruglevert. Dat heet &lsquo;salderen&rsquo;. Heb je een slimme meter? Dan trekken we de door jou teruggeleverde stroom eerst af van je normaal- en daarna van je dalverbruik. Salderen kan alleen als je je zonnepanelen op energieleveren.nl hebt aangemeld.&nbsp;De salderingsregeling wordt per 1 januari 2027 afgeschaft. Vanaf dan kun je de stroom die je teruglevert met zonnepanelen niet meer verrekenen (salderen) met de stroom die je afneemt.&nbsp;<br />\n&nbsp;<br />\n<strong>Terugleververgoeding&nbsp;&nbsp;</strong><br />\nLever je meer stroom terug dan je totale stroomverbruik? Dan ontvang je een terugleververgoeding per kWh (exclusief overheidsheffingen en btw). De terugleververgoeding zie je op je jaarnota.&nbsp;<br />\n<br />\n<span style="color: #2f2d2d;">Bij een contract met vaste looptijd van 3 jaar gebruiken we twee tariefperiodes voor terugleverkosten en terugleververgoeding. Dit is vanwege de afschaffing van de salderingsregeling. De eerste periode begint bij de start van je contract en loopt tot 1 januari 2027. De tweede periode begint op 1 januari 2027 en loopt tot de vaste looptijd van je contract voorbij is.&nbsp;<span data-teams="true">De terugleverkosten en -vergoeding wijzigen in je contract op 1 januari 2027. Verder veranderen deze tarieven niet tijdens de vaste looptijd van het contract. De andere voorwaarden van het contract blijven gedurende de hele looptijd van het contract gelijk.</span></span></p>',
        },
      },
      redeliveryDynamicPricingParagraph: {
        value: {
          title: 'Teruglevering',
          content:
            'Als je zelf stroom opwekt - en dit niet direct zelf gebruikt - lever je deze opgewekte stroom terug. Dit kan alleen als je aangemeld bent op <a rel="noopener noreferrer" href="https://energieleveren.nl" target="_blank">energieleveren.nl</a>. Je ontvangt voor teruggeleverde stroom het werkelijke uurtarief per kWh op het moment van terugleveren, inclusief de inkoopvergoeding en overheidsheffingen en btw. Lever je over het gehele verbruiksjaar, waar de (jaar)nota op betrekking heeft, meer terug dan dat je verbruikt, dan ontvang je, in afwijking van artikel 3 van het Voorwaardenoverzicht, voor het netto teruggeleverde volume de marktprijs per kWh, die in het uur van teruglevering geldt (exclusief de inkoopvergoeding en overheidsheffingen en btw) min de verkoopvergoeding (<span style="background-color: #ffffff; color: #000000;">&euro; 0,02923 per kWh tot en met 31 juli 2025 ,&euro; 0,02398 vanaf 1 augustus 2025)</span>. De verkoopvergoeding is ter dekking van de kosten die Eneco maakt voor de verkoop en afhandeling van stroom op de energiemarkt en kan wijzigen, waardoor de vergoeding voor netto teruggeleverde stroom kan fluctueren.',
        },
      },
      lowHighTariffDifferenceParagraph: {
        value: {
          title: 'Normaal- en daltarief',
          content:
            'We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat normaal- en daltarieven steeds dichter bij elkaar liggen of gelijk zijn.',
        },
      },
      timeOfUseLowHighTariffDifferenceParagraph: {
        value: {
          title: 'Standaard- en voordeeltarieven',
          content:
            'We wekken met elkaar steeds meer duurzame stroom op, voornamelijk overdag. Vraag en aanbod verandert hierdoor, wat voor andere prijzen op de energiemarkt zorgt. Dit heeft tot gevolg dat standaard- en voordeeltarieven steeds dichter bij elkaar liggen of gelijk zijn.',
        },
      },
    },
    electricityCostDetails: {
      redeliveryCostLabel: { value: 'Terugleverkosten' },
      redeliveryMoreNotification: {
        value: {
          variant: 'info',
          title: 'Je levert meer terug dan je verbruikt',
          content:
            '<p>Je totale stroomverbruik ({electricityTotal} kWh) is verminderd met de teruggeleverde stroom van je zonnepanelen ({electricityRedelivery} kWh). Dat heet salderen. We verrekenen dit met je eerstvolgende jaarnota.</p>',
        },
      },
      standingChargeLabel: { value: 'Vaste leveringskosten' },
      storageSustainableEnergyLabel: { value: 'Opslag duurzame energie' },
      tariffLabel: { value: 'Leveringskosten (normaaltarief)' },
      timeOfUseTariffLabel: { value: 'Leveringskosten (standaardtarief)' },
      deliveryCostsLabel: { value: 'Netbeheerkosten (3 x 25 Ampère aansluiting)' },
      energyTaxLabel: { value: 'Energiebelasting' },
      lowTarifflLabel: { value: 'Leveringskosten (daltarief)' },
      timeOfUseLowTariffLabel: { value: 'Leveringskosten (voordeeltarief)' },
      redeliveryLessNotification: {
        value: {
          variant: 'info',
          title: 'Je verbruikt meer dan dat je teruglevert',
          content:
            '<p>Je totale stroomverbruik ({electricityTotal} kWh) is verminderd met de teruggeleverde stroom van je zonnepanelen ({electricityRedelivery} kWh). Dat heet salderen.</p>\n<p>We verrekenen dit met je eerstvolgende jaarnota.</p>',
        },
      },
      taxDiscountLabel: { value: 'Verminderde energiebelasting' },
      redeliveryTariffLabel: { value: 'Tarief voor je opwek na saldering (geen btw)' },
      tariffDeliveryCostsLabel: { value: 'Leveringskosten' },
      redeliveryLabel: { value: 'Teruglevering' },
      redeliveryDynamicPricingProductTariffLabel: { value: 'Terugleververgoeding' },
      tariffDynamicPricingProductDeliveryCostsLabel: { value: 'Verwachte gemiddelde tarief' },
      taxBracket2Label: { value: 'per jaar' },
      taxBracket1Label: { value: 'per jaar' },
      governmentLeviesLabel: { value: 'Overheidsheffingen' },
      taxBracket3Label: { value: 'per jaar' },
      redeliveryCompensationEndDateText: { value: 'Terugleververgoeding tot {date}' },
      redeliveryCompensationRateDialog: {
        value: { title: '', content: '', triggerText: '', submitButtonText: '', cancelButtonText: '' },
      },
      redeliveryCompensationRateText: { value: 'De terugleververgoeding is per kWh vanaf {date}.' },
      redeliveryCostsEndDateText: { value: 'Terugleverkosten tot {date}' },
      redeliveryCostsRateDialog: {
        value: { title: '', content: '', triggerText: '', submitButtonText: '', cancelButtonText: '' },
      },
      redeliveryCostsRateText: { value: 'De terugleverkosten zijn per kWh vanaf {date}.' },
    },
    footer: {
      electricityHighLabel: { value: 'Normaal' },
      electricityLabel: { value: 'Stroom' },
      electricityLowLabel: { value: 'Dal' },
      footerDescription: { value: 'Aanbod voor <strong>{address}</strong> op basis van' },
      gasLabel: { value: 'Gas' },
      modifyTriggerText: { value: 'Mijn verbruik aanpassen' },
      redeliveryLabel: { value: 'Teruglevering' },
      warmthLabel: { value: 'Warmte' },
      waterLabel: { value: 'Water' },
    },
    gasCostDetails: {
      deliveryCostsLabel: { value: 'Netbeheerkosten' },
      energyTaxLabel: { value: 'Energiebelasting' },
      standingChargeLabel: { value: 'Leveringskosten' },
      storageSustainableEnergyLabel: { value: 'Opslag duurzame energie' },
      tariffLabel: { value: 'Leveringskosten' },
      governmentLeviesLabel: { value: 'Overheidsheffingen' },
      taxBracket1Label: { value: 'per jaar' },
      taxBracket2Label: { value: 'per jaar' },
    },
    genericError: {
      errorNotification: {
        value: {
          variant: 'error',
          title: 'We kunnen je op dit moment geen aanbod doen.',
          content:
            'Er is een technisch probleem ontstaan. Laad de pagina opnieuw, of neem contact op met de&nbsp;<a rel="noopener noreferrer" href="https://www.eneco.nl/klantenservice/" target="_blank">Klantenservice</a>.',
        },
      },
      tryAgainButtonText: { value: 'Laad de pagina opnieuw' },
      errorNotAvailableNotification: {
        value: {
          variant: 'error',
          title: 'Op dit moment zijn er geen vaste contracten beschikbaar',
          content:
            'Vaste contracten zijn tijdelijk uitverkocht. Waarschijnlijk zijn er vanaf morgen weer contracten beschikbaar; houd onze website in de gaten voor het aanbod. Dringend stroom en/of gas nodig? Sluit dan telefonisch een&nbsp;<a href="/duurzame-energie/modelcontract/">modelcontract</a>&nbsp;af.\n<p>&nbsp;</p>',
        },
      },
      errorNotFoundNotification: {
        value: {
          variant: 'error',
          title: 'We kunnen je op dit moment geen aanbod doen',
          content:
            'Graag helpen we je via onze klantenservice. We zijn op maandag tot en met vrijdag van 08.00 tot 16.00 bereikbaar. Telefoon <a href="tel:0888955955">088-8 955 955</a> (gebruikelijke belkosten).',
        },
      },
    },
    giftData: {
      giftProductDataList: [
        {
          id: 'c616daa6-d2c1-4efb-90c8-595481469c32',
          url: 'https://www.test.eneco.nl/library/gift-product-data-library-folder/gamma-giftcard-150---df/',
          name: 'Gamma Giftcard 150 - DF',
          displayName: 'Gamma Giftcard 150 - DF',
          fields: {
            gift: { giftId: { value: 'POAA12AAxxxxxGC15xx' } },
          },
        },
        {
          id: '7eadb1a4-2814-4e83-ac5d-19f27b7ea231',
          url: 'https://www.test.eneco.nl/library/gift-product-data-library-folder/gamma-giftcard-180---df/',
          name: 'Gamma Giftcard 180 - DF',
          displayName: 'Gamma Giftcard 180 - DF',
          fields: {
            gift: { giftId: { value: 'POAA12AAxxxxxGC18xx' } },
          },
        },
        {
          id: '4cb79653-3d6f-4c97-b6dc-7136d1a3cda3',
          url: 'https://www.test.eneco.nl/library/gift-product-data-library-folder/gamma-giftcard-200---df/',
          name: 'Gamma Giftcard 200 - DF',
          displayName: 'Gamma Giftcard 200 - DF',
          fields: {
            gift: { giftId: { value: 'POAA12AAxxxxxGC20xx' } },
          },
        },
        {
          id: '275fa75e-8bfc-450f-8adf-49b39d460906',
          url: 'https://www.test.eneco.nl/library/gift-product-data-library-folder/gamma-giftcard-250---df/',
          name: 'Gamma Giftcard 250 - DF',
          displayName: 'Gamma Giftcard 250 - DF',
          fields: {
            gift: { giftId: { value: 'POAA36AAxxxxxGC25xx' } },
          },
        },
        {
          id: 'ac9e512a-3396-4506-ab1d-a43564baf52a',
          url: 'https://www.test.eneco.nl/library/gift-product-data-library-folder/gamma-giftcard-50---sf-e/',
          name: 'Gamma Giftcard 50 - SF-E',
          displayName: 'Gamma Giftcard 50 - SF-E',
          fields: {
            gift: { giftId: { value: 'POxA12AAxxxxxGC50xx' } },
          },
        },
        {
          id: 'f090556e-28c4-4c66-b773-c2c959acf544',
          url: 'https://www.test.eneco.nl/library/gift-product-data-library-folder/gamma-giftcard-75---sf-e/',
          name: 'Gamma Giftcard 75 - SF-E',
          displayName: 'Gamma Giftcard 75 - SF-E',
          fields: {
            gift: { giftId: { value: 'POxA12AAxxxxxGC75xx' } },
          },
        },
      ],
      giftExplanationTriggerText: { value: 'meer info' },
    },
    notifications: {
      dynamicPricingContractNotification: { value: { variant: 'warning', title: '', content: '' } },
      hybridContractNotification: { value: { variant: 'warning', title: '', content: '' } },
    },
    productTypeLabels: {
      electricityProductLabel: { value: 'Groene stroom' },
      gasProductLabel: { value: 'Gas' },
      geothermalHeatingProductLabel: {
        value: 'Bronwarmte',
      },
      warmthProductLabel: {
        value: 'Warmte',
      },
      waterProductLabel: {
        value: 'Warm water',
      },
      warmthExchangeSetLabel: {
        value: 'Afleverset',
      },
      cashbackDirectLabel: {
        value: 'CashBack',
      },
      cashbackOnYearNoteLabel: {
        value: 'CashBack',
      },
    },
    promotionTexts: {
      cashbackDirectRibbonContent: { value: '{cashBackDirect} Welkomstbonus' },
      cashbackOnYearNoteRibbonContent: { value: '{cashBackOnYearNote} Loyaliteitsbonus' },
      cashbackDirectLabelContent: {
        value: 'Eenmalige welkomstbonus',
      },
      cashbackOnYearNoteLabelContent: { value: 'Eenmalige loyaliteitsbonus' },
      cashbackDirectExplanationContent: {
        value:
          'We zijn blij dat je voor ons kiest en daarom krijg je een welkomstbonus. Deze ontvang je via een eenmalige creditnota voor of op de betaaldatum van je tweede termijnbedrag.',
      },
      cashbackExplanationTriggerText: { value: 'meer info' },
      cashbackOnYearNoteExplanationContent: {
        value:
          'Kies voor Eneco en ontvang met je eerste jaarnota een loyaliteitsbonus. Dit verrekenen we als een eenmalige korting op je eerste jaarnota.<br />',
      },
      timeOfUseRibbonContent: {
        value: 'tou',
      },
    },
    warmthCostDetails: {
      variableCostsWarmthLabel: { value: 'Variabele kosten warmte' },
      variableCostsWaterLabel: { value: 'Variabele kosten water' },
      tariffColdWaterLabel: { value: 'Verbruik koud water t.b.v. warm water' },
      tariffLabel: { value: 'Verbruik warmte' },
      tariffWarmWaterLabel: { value: 'Verbruik warmte voor warm water' },
      taxColdWaterLabel: { value: 'Belasting op leidingwater' },
      deliveryCostsLabel: { value: 'Meettarief' },
      rentWarmthSet: { value: 'Huur warmteafleverset' },
      standingChargeLabel: { value: 'Vastrecht' },
      variableCostsGeothermalHeatingLabel: { value: 'Opslag vastrecht bronwarmte (boven 3 kW)' },
      geothermalHeatingPompCostsLabel: { value: 'Huur warmtepomp' },
      standingChargeGeothermalHeatingLabel: { value: 'Vastrecht bronwarmte (basistarief voor 3 kW)' },
    },
  },
  datasourceRequired: true,
};
