import React, { FC, useEffect, useState } from 'react';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import useDC from '@dc/useDC';
import { Overview } from '@eneco/flows';
import { Header, Layout, Price } from '@eneco/flows2';
import { useFlowHooks } from '@eneco/flows2/src/utils/FlowProvider';
import { useFormatter } from '@i18n';
import { usePlaceholderContent } from '@sitecore/common';
import { OverviewStepRendering } from '@sitecore/types/OverviewStep';
import { Box, Button, Bleed, IconList, NotificationBox, Stack, Text, TextLink } from '@sparky';
import { useMediaQuery } from '@sparky/hooks';
import { CalendarIcon, ElectricityIcon, RecycleIcon } from '@sparky/icons';

import { Ribbon, PriceDetailsOverview, OverviewConsumption, Error } from '../../components';
import ChatActivator from '../../components/ChatActivator/ChatActivator';
import { FlowConsumerContextEnergy } from '../../context';
import {
  getCashbackTexts,
  getOffer,
  getProductIconTypeFromProductType,
  getProductTitleFromProductType,
  checkOfferIncludesDynamicPricingProduct,
  checkOfferIncludesNonDynamicPricingProduct,
} from '../../helpers';
import { ENECO_ENERGY_FLOW_GIFT } from '../../helpers/constants';
import { checkOffersIncludeVariablePricingProduct } from '../../helpers/misc';
import { useFlowTracking } from '../../hooks';
import { useOfferMachine } from '../../hooks/useOfferMachine';
import { EnergyType } from '../../types';

export const OverviewStep: FC = () => {
  const { businessUnit, label } = useDC();
  const { useFlowSelector, useFlowActorRef } = useFlowHooks<FlowConsumerContextEnergy>();
  const flowState = useFlowSelector(state => state);
  const sendToFlowMachine = useFlowActorRef().send;

  const {
    context: flowContext,
    context: {
      energyType,
      street,
      houseNumber,
      houseNumberSuffix,
      city,
      hasDoubleMeter,
      hasSolarPanels,
      usageElectricityHigh,
      usageElectricityLow,
      usageGas,
      usageWarmth,
      usageWater,
      solarPanelsOutput,
      isSME,
    },
  } = flowState;

  const { offerState, sendToOfferMachine } = useOfferMachine(businessUnit, label);

  const { context: { offer } = {} } = offerState;

  const { OverviewStep: { fields: textualData } = {} } = usePlaceholderContent<{
    OverviewStep: OverviewStepRendering;
  }>();

  const { trackBeginCheckout, trackViewCart } = useFlowTracking();

  const { format, address, currency } = useFormatter();

  const [isSubmitSuccessful, setIsSubmitSuccessful] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [isFailed, setIsFailed] = useState(false);
  const isLargeBreakpoint = useMediaQuery('lg');

  const [isNotFound, setIsNotFound] = useState(false);
  const [isNotAvailable, setIsNotAvailable] = useState(false);

  const selectedOffer = getOffer(flowContext, offer);
  const selectedCashbackTexts = getCashbackTexts(
    selectedOffer?.costDiscounts?.cashback?.type,
    textualData?.promotionTexts,
    textualData?.giftData,
    selectedOffer?.costDiscounts?.cashback?.value,
  );

  const shouldShowDynamicPricingContractNotification =
    !checkOffersIncludeVariablePricingProduct(selectedOffer) &&
    checkOfferIncludesDynamicPricingProduct(selectedOffer) &&
    !checkOfferIncludesNonDynamicPricingProduct(selectedOffer) &&
    !!textualData?.notifications?.dynamicPricingContractNotification?.value?.content;

  const shouldShowHybridContractNotification =
    checkOfferIncludesDynamicPricingProduct(selectedOffer) &&
    checkOfferIncludesNonDynamicPricingProduct(selectedOffer) &&
    !!textualData?.notifications?.hybridContractNotification?.value?.content;

  const costsType = isSME ? 'vatExcluded' : 'vatIncluded';

  useEffect(() => {
    sendToOfferMachine({
      type: 'GET_OFFER',
      flowContext: { ...flowContext },
    });
  }, []);

  useEffect(() => {
    if (isSubmitSuccessful) {
      if (selectedOffer) trackBeginCheckout(selectedOffer, offer);

      sendToFlowMachine({
        type: 'NEXT_PAGE',
      });
    }
  }, [isSubmitSuccessful]);

  useEffect(() => {
    if (!offerState.matches('IDLE')) {
      setIsLoading(offerState.matches('FETCHING'));
      setIsFailed(!offerState.matches('FETCHING') && !offerState.matches('SUCCESS'));
      setIsSuccessful(offerState.matches('SUCCESS'));

      setIsNotFound(offerState.matches('ERROR_NOT_FOUND'));
      setIsNotAvailable(offerState.matches('ERROR_NOT_AVAILABLE'));
    }
    if (offerState.matches('SUCCESS')) {
      if (selectedOffer) trackViewCart(selectedOffer, offer);
    }
  }, [offerState]);

  return (
    <>
      <Layout.Content variant="B" isLoading={isLoading}>
        {isSuccessful && (
          <>
            <Header align="center">
              <Header.Title>{textualData?.content?.title?.value}</Header.Title>
              {textualData?.content?.text?.value && <Header.Text>{textualData.content.text.value}</Header.Text>}
            </Header>

            {textualData?.content?.notification?.value?.content && (
              <NotificationBox
                isAlert={false}
                variant={textualData?.content?.notification?.value?.variant}
                title={textualData?.content?.notification?.value?.title}
                text={<RichText html={textualData?.content?.notification?.value?.content} />}
              />
            )}

            {shouldShowDynamicPricingContractNotification && (
              <TrackedNotificationBox
                isAlert={false}
                label={textualData?.notifications?.dynamicPricingContractNotification?.value?.title}
                title={textualData?.notifications?.dynamicPricingContractNotification?.value?.title ?? ''}
                text={
                  <RichText html={textualData?.notifications?.dynamicPricingContractNotification?.value?.content} />
                }
                variant={textualData?.notifications?.dynamicPricingContractNotification?.value?.variant}
              />
            )}

            {shouldShowHybridContractNotification && (
              <TrackedNotificationBox
                isAlert={false}
                label={textualData?.notifications?.hybridContractNotification?.value?.title}
                title={textualData?.notifications?.hybridContractNotification?.value?.title ?? ''}
                text={<RichText html={textualData?.notifications?.hybridContractNotification?.value?.content} />}
                variant={textualData?.notifications?.hybridContractNotification?.value?.variant}
              />
            )}

            <Stack.Item>
              <Overview>
                {selectedCashbackTexts ? (
                  <Bleed top="6">
                    <Ribbon
                      size="large"
                      emphasis="high"
                      title={format(selectedCashbackTexts.title, {
                        [`${selectedOffer?.costDiscounts?.cashback?.type}`]: currency.euroNoFractionDigits(
                          selectedOffer?.costDiscounts?.cashback?.value ?? 0,
                        ),
                      })}
                      text={selectedCashbackTexts?.text}
                      trigger={selectedCashbackTexts?.trigger}
                    />
                  </Bleed>
                ) : null}

                {selectedOffer?.products?.map(product => (
                  <Overview.Product
                    key={product.type}
                    type={getProductIconTypeFromProductType(product.type)}
                    title={getProductTitleFromProductType(product.type, textualData?.productTypeLabels)}
                    subtitle={product.description}
                    costsMonthly={{
                      value: product.costsMonthly?.[costsType] ?? 0,
                      period: textualData?.costDetails?.perMonthLabel?.value,
                    }}
                    costsYearly={{
                      value: product.costsYearly?.[costsType] ?? 0,
                      period: textualData?.costDetails?.perYearLabel?.value,
                    }}
                  />
                ))}

                <Overview.ProductSummary
                  label={textualData?.costDetails?.totalCostsLabel?.value}
                  costs={{
                    value: selectedOffer?.costsMonthly?.[costsType] ?? 0,
                    period: textualData?.costDetails?.perMonthLabel?.value,
                  }}
                  costDetailsTrigger={
                    <Text size="BodyS">
                      <TrackedDialog
                        title={textualData?.content?.priceDetailsDialog?.value?.title}
                        description={<RichText html={textualData?.content?.priceDetailsDialog?.value?.content} />}
                        trigger={<TextLink>{textualData?.content?.priceDetailsDialog?.value?.triggerText}</TextLink>}>
                        <PriceDetailsOverview
                          offer={selectedOffer}
                          flowContext={flowContext}
                          textualData={textualData}
                        />
                      </TrackedDialog>
                    </Text>
                  }>
                  <Box paddingTop="2" paddingBottom="1">
                    <Text size="BodyS" align="right">
                      {/* whenever the "costsYearlyPromotion" or the "cashback" value is defined 
                      we should render different text label and different price */}
                      <Price
                        suffix={textualData?.costDetails?.[
                          selectedOffer?.costsYearlyPromotion || selectedOffer?.costDiscounts?.cashback?.value
                            ? 'totalCostsFirstYearLabel'
                            : 'perYearLabel'
                        ]?.value?.toLowerCase()}
                        emphasis="low">
                        {selectedOffer?.costsYearlyPromotion?.[costsType] ??
                          Number(selectedOffer?.costsYearly?.[costsType] ?? 0) -
                            (selectedOffer?.costDiscounts?.cashback?.type === ENECO_ENERGY_FLOW_GIFT
                              ? 0
                              : Number(selectedOffer?.costDiscounts?.cashback?.value ?? 0))}
                      </Price>
                    </Text>
                  </Box>
                </Overview.ProductSummary>

                <Overview.Content>
                  <Stack direction="column" gap="3">
                    <Button
                      isLoading={isSubmitSuccessful}
                      onClick={() => {
                        setIsSubmitSuccessful(true);
                      }}>
                      {textualData?.content?.nextStepText?.value}
                    </Button>

                    <Text size="BodyS" align="center" color="textLowEmphasis">
                      {textualData?.content?.priceIncludingText?.value}
                    </Text>
                  </Stack>
                </Overview.Content>

                <Overview.Footer>
                  <IconList>
                    <IconList.Item icon={<RecycleIcon color="iconSecondary" />}>
                      <Text size="BodyS" color="textLowEmphasis">
                        <RichText html={textualData?.content?.usp1Content?.value} />
                      </Text>
                    </IconList.Item>
                    <IconList.Item icon={<ElectricityIcon color="iconSecondary" />}>
                      <Text size="BodyS" color="textLowEmphasis">
                        <RichText html={textualData?.content?.usp2Content?.value} />
                      </Text>
                    </IconList.Item>
                    <IconList.Item icon={<CalendarIcon color="iconSecondary" />}>
                      <Text size="BodyS" color="textLowEmphasis">
                        <RichText html={textualData?.content?.usp3Content?.value} />
                      </Text>
                    </IconList.Item>
                  </IconList>
                </Overview.Footer>
              </Overview>
            </Stack.Item>
            {isLargeBreakpoint && (
              <Stack.Item>
                <ChatActivator
                  text="Vraag over energiecontracten?"
                  topic="start_salesflow_looptijd"
                  trigger="De chatbot helpt je graag."
                />
              </Stack.Item>
            )}
          </>
        )}

        {isFailed && (
          <Error
            textualData={{
              notification: isNotAvailable
                ? textualData?.genericError?.errorNotAvailableNotification
                : isNotFound
                  ? textualData?.genericError?.errorNotFoundNotification
                  : textualData?.genericError?.errorNotification,
              buttonLabel: !isNotAvailable && !isNotFound ? textualData?.genericError?.tryAgainButtonText : undefined,
            }}
          />
        )}
      </Layout.Content>

      {/* // won't render footer for energyTypes without usage value */}
      {energyType !== EnergyType.GeothermalHeating && (
        <Layout.Footer isLoading={isLoading || isFailed}>
          <OverviewConsumption
            textualData={{
              description: format(textualData?.footer?.footerDescription?.value ?? '', {
                address: address.medium({ street, houseNumber, houseNumberSuffix, city }),
              }),
              redeliveryLabel: textualData?.footer?.redeliveryLabel?.value,
              electricityLabel: textualData?.footer?.electricityLabel?.value,
              electricityHighLabel: textualData?.footer?.electricityHighLabel?.value,
              electricityLowLabel: textualData?.footer?.electricityLowLabel?.value,
              gasLabel: textualData?.footer?.gasLabel?.value,
              warmthLabel: textualData?.footer?.warmthLabel?.value,
              waterLabel: textualData?.footer?.waterLabel?.value,
            }}
            data={{
              hasDoubleMeter,
              hasSolarPanels,
              usageElectricityHigh,
              usageElectricityLow,
              usageGas,
              usageWarmth,
              usageWater,
              solarPanelsOutput,
            }}
            editButton={{
              label: textualData?.footer?.modifyTriggerText?.value ?? '',
              onClick: () => sendToFlowMachine({ type: 'GOTO', stepName: 'STEP_USAGE' }),
            }}
          />
        </Layout.Footer>
      )}
    </>
  );
};
