import { FeaturesReady } from '@growthbook/growthbook-react';

import { Layout } from '@eneco/flows2';
import { createFlowProvider } from '@eneco/flows2/utils';

import { FlowConsumerContextEnergy } from './context';
import { routes, actions, initialContext } from './EnergyFlowOxxio.config';

const Flow = () => (
  <FeaturesReady fallback={null} timeout={1000}>
    <EnergyFlow />
  </FeaturesReady>
);

const EnergyFlow = () => {
  const { Provider, Navigation, Router } = createFlowProvider<FlowConsumerContextEnergy>({
    routes,
    actions,
    initialContext,
  });

  return (
    <Provider>
      <Layout>
        <Layout.Header>
          <Navigation sitecoreComponentName="Navigation" />
        </Layout.Header>
        <Router />
      </Layout>
    </Provider>
  );
};

export default Flow;
