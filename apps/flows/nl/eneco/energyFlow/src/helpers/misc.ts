import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';

import {
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_MOVING_OUT,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_SWITCH,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX_URGENT,
  ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_MOVING_OUT,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_SWITCH,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX_URGENT,
  OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_GAS,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH,
  ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_WATER,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_GAS,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH,
  OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_WATER,
} from './constants';
import { EnergyType, Offer, ContractOption } from '../types';
import { OfferType } from '../types/enums';

export const checkOfferIncludesDynamicOrVariablePricingProduct = (offer?: Offer): OfferType => {
  if (!offer) return OfferType.None;

  const hasZeroDurationProduct = offer.products.some(
    product => (product.type === 'gas' || product.type === 'electricity') && product.contractSpan?.duration === 0,
  );

  if (hasZeroDurationProduct) {
    if (offer.discountType === OfferType.Variable) return OfferType.Variable;
    if (offer.discountType?.includes(OfferType.Dynamic)) return OfferType.Dynamic;
  }

  return OfferType.Fixed;
};

export const checkOfferIncludesDynamicPricingProduct = (offer?: Offer): boolean =>
  !!offer?.products.find(
    product => (product.type === 'gas' || product.type === 'electricity') && product?.contractSpan?.duration === 0,
  );

export const filterOffersByType = (offers: Offer[], productFilter: string): Offer[] => {
  return offers.filter(({ discountType }) => {
    if (!discountType) return false;

    if (productFilter === 'dynamic') {
      return ['dynamic', 'Hybrid'].some(type => discountType.includes(type));
    }

    if (productFilter === 'variabel') {
      return discountType.includes('variable');
    }

    if (productFilter === 'fixed') {
      return !['dynamic', 'Hybrid', 'variable'].some(type => discountType.includes(type));
    }

    return discountType.includes(productFilter);
  });
};

export const checkOfferTypes = (contractOptions: ContractOption[], offers: Offer[]): ContractOption[] => {
  const filteredOptions = contractOptions.filter(option =>
    getAvailableOfferTypes(offers).some(offer => offer?.includes(option.name)),
  );
  return filteredOptions;
};

export const getAvailableOfferTypes = (offers: Offer[]) => {
  const availableOfferTypes: string[] = [];
  offers.filter(({ discountType }) => {
    if (!discountType) return false;

    if (['dynamic', 'Hybrid'].some(type => discountType.includes(type))) {
      availableOfferTypes.push(OfferType.Dynamic);
    }

    if (discountType.includes('variable')) {
      availableOfferTypes.push(OfferType.Variable);
    }

    if (!['dynamic', 'Hybrid', 'variable'].some(type => discountType.includes(type))) {
      availableOfferTypes.push(OfferType.Fixed);
    }
  });
  return [...new Set(availableOfferTypes)];
};

export const checkIfOfferHasTimeOfUseProduct = (offer?: Offer) => {
  return !!offer?.products?.some?.(product => product.priceDifferentiation === 'TimeOfUse');
};

export const checkOfferIncludesNonDynamicPricingProduct = (offer?: Offer): boolean =>
  !!offer?.products.find(
    product => (product.type === 'gas' || product.type === 'electricity') && product?.contractSpan?.duration !== 0,
  ) || checkIfOfferHasTimeOfUseProduct(offer);

export const checkOffersIncludesDynamicPricingProduct = (offers: Offer[]): boolean => {
  return !!offers.find(offer => checkOfferIncludesDynamicPricingProduct(offer));
};

export const checkOffersIncludeVariablePricingProduct = (offer?: Offer): boolean => {
  return !!offer?.discountType?.includes(OfferType.Variable);
};

export const checkAvailabilityOfferType = (offers: Offer[], discountCode?: string): string | undefined =>
  offers.find(offer => offer.type === discountCode)?.type;

export const selectDefaultDiscountCode = (offers: Offer[]): string | undefined =>
  (offers.find(offer => offer.costDiscounts?.cashback || offer.mostPopularChoice) ?? offers[0])?.type;

export const energyTypeIncludes = (energyType: EnergyType, type: EnergyType): boolean =>
  RegExp(`${type}`, 'i').test(energyType);

export const getMinContractStartDateInDays = (
  isMoving: boolean,
  isUrgent: boolean,
  label: DC_Repositories_Base_Enumerations_Label,
): number => {
  return isUrgent
    ? label === 'eneco'
      ? ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT
      : OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT
    : isMoving
      ? label === 'eneco'
        ? ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_MOVING_OUT
        : OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_MOVING_OUT
      : label === 'eneco'
        ? ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_SWITCH
        : OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_SWITCH;
};

export const getMaxContractStartDateInDays = (label: DC_Repositories_Base_Enumerations_Label) =>
  label === 'eneco'
    ? ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX
    : OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX;

export const getMinUrgentContractStartDateInDays = (label: DC_Repositories_Base_Enumerations_Label) =>
  label === 'eneco'
    ? ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT
    : OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MIN_URGENT;

export const getMaxUrgentContractStartDateInDays = (label: DC_Repositories_Base_Enumerations_Label) =>
  label === 'eneco'
    ? ENECO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX_URGENT
    : OXXIO_ENERGY_FLOW_CONTRACT_START_DATE_IN_DAYS_MAX_URGENT;

export const getMaxUsageValue = (
  type: 'ELECTRICITY_HIGH' | 'ELECTRICITY_LOW' | 'GAS' | 'WARMTH' | 'WATER',
  label: DC_Repositories_Base_Enumerations_Label,
): number => {
  if (type === 'ELECTRICITY_HIGH') {
    return label === 'eneco'
      ? ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH
      : OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_HIGH;
  } else if (type === 'ELECTRICITY_LOW') {
    return label === 'eneco'
      ? ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW
      : OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_ELECTRICITY_LOW;
  } else if (type === 'GAS') {
    return label === 'eneco' ? ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_GAS : OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_GAS;
  } else if (type === 'WARMTH') {
    return label === 'eneco' ? ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH : OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_WARMTH;
  } else if (type === 'WATER') {
    return label === 'eneco' ? ENECO_ENERGY_FLOW_MAX_USAGE_VALUE_WATER : OXXIO_ENERGY_FLOW_MAX_USAGE_VALUE_WATER;
  } else {
    return 0;
  }
};

/**
 * Sorts contract options so that 'Vast', 'Variable', 'Dynamic' appear in this order if present.
 */
export const sortContractOptionsByPreferredOrder = (contractOptions: ContractOption[]): ContractOption[] => {
  const contractOrder = ['Vast', 'Variable', 'Dynamic'];
  return [...contractOptions].sort((a, b) => {
    const aLabel = a.label || a.name;
    const bLabel = b.label || b.name;
    const aIndex = contractOrder.indexOf(aLabel);
    const bIndex = contractOrder.indexOf(bLabel);
    if (aIndex === -1 && bIndex === -1) return 0;
    if (aIndex === -1) return 1;
    if (bIndex === -1) return -1;
    return aIndex - bIndex;
  });
};
