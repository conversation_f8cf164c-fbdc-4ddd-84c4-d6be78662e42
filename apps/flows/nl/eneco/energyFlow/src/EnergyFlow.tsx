import { useMemo } from 'react';

import { FeaturesReady } from '@growthbook/growthbook-react';

import { SpinnerIconContainer } from '@custom-components/flows/Layout';
import { useRouter } from '@dxp-next';
import { Layout } from '@eneco/flows2';
import { createFlowProvider } from '@eneco/flows2/utils';
import { Box } from '@sparky';
import { SpinnerIcon } from '@sparky/icons';

import { FlowConsumerContextEnergy } from './context';
import { actions, initialContext, routes as routes_A } from './EnergyFlow.config';
import { actions as actions_G, initialContext as initialContext_G, routes as routes_G } from './EnergyFlow_G.config';
import { actions as actions_H, initialContext as initialContext_H, routes as routes_H } from './EnergyFlow_H.config';
import { ExperimentProvider, useExperimentFlowProvider } from './growthbook';
import { TestConfigVariant } from './types/types';

const EXCLUDE_TOU_EXPERIMENT = 'excludeTouExperiment';
const testConfigs: TestConfigVariant[] = [
  {
    testKey: '639064|salesflow-offerstep-expandable-productcards',
    variant: 'G',
    routes: routes_G,
    actions: actions_G,
    initialContext: initialContext_G,
  },
  {
    testKey: '639064|salesflow-offerstep-expandable-productcards',
    variant: 'H',
    routes: routes_H,
    actions: actions_H,
    initialContext: initialContext_H,
  },
];

const defaultConfig = {
  routes: routes_A,
  actions: actions,
  initialContext: initialContext,
};

const queryParamNames = [EXCLUDE_TOU_EXPERIMENT];

const Flow = () => {
  const { query: queryParams } = useRouter();
  const excludeExperiment = Boolean(queryParams[EXCLUDE_TOU_EXPERIMENT]);

  const attributes = useMemo(
    () => ({
      excludeExperiment,
    }),
    [excludeExperiment],
  );

  return (
    <ExperimentProvider testConfigs={testConfigs} defaultConfig={defaultConfig} attributes={attributes}>
      <FeaturesReady fallback={null} timeout={1000}>
        <EnergyFlow />
      </FeaturesReady>
    </ExperimentProvider>
  );
};

const EnergyFlow = () => {
  const flowExperimentProviderProps = useExperimentFlowProvider();
  const { routes, actions, initialContext, isLoading } = flowExperimentProviderProps;

  const { Provider, Navigation, Router } = useMemo(
    () =>
      createFlowProvider<FlowConsumerContextEnergy>({
        routes,
        actions,
        initialContext,
        stickyQueryParams: queryParamNames,
      }),
    [routes, actions, initialContext],
  );

  return (
    <div>
      {isLoading ? (
        <SpinnerIconContainer alignY="center" alignX="center">
          <Box paddingBottom="32">
            <SpinnerIcon size="large" color="iconBrand" />
          </Box>
        </SpinnerIconContainer>
      ) : (
        <Provider>
          <Layout>
            <Layout.Header>
              <Navigation sitecoreComponentName="Navigation" />
            </Layout.Header>
            <Router />
          </Layout>
        </Provider>
      )}
    </div>
  );
};

export default Flow;
