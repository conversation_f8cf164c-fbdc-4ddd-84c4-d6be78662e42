import { useEffect } from 'react';

import { getStaticPrefix } from '@common/env';
import { EndStepCard } from '@components/flow/EndStep/Card';
import { EndStepHero } from '@components/flow/EndStep/EndStepHero';
import useDC from '@dc/useDC';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { PaymentArrangementEndStepRendering } from '@sitecore/types/PaymentArrangementEndStep';
import { useTracking } from '@tracking';

import { Steps } from '../Router/Router';

export const EndStep = () => {
  const { fields } = useContent<PaymentArrangementEndStepRendering>();
  const { trackFunnelCompleted, trackPageView } = useTracking();
  const { activePath } = useRouter();
  const { label } = useDC();

  const { title, content, cta, image } = fields ?? {};

  useEffect(() => {
    trackFunnelCompleted({ step: Steps.THANK_YOU });
  }, [activePath, trackFunnelCompleted, trackPageView]);

  return (
    <EndStepHero
      heroImage={{
        value: { src: image?.value?.src ?? `${getStaticPrefix()}/images/${label}/paymentarrangement/endstep.png` },
      }}>
      <EndStepCard
        title={title?.value}
        content={content?.value}
        buttonHref={cta?.value?.href}
        buttonText={cta?.value?.text}
      />
    </EndStepHero>
  );
};
