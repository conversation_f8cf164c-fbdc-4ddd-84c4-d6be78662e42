import { FC } from 'react';

import { getStaticPrefix } from '@common/env';
import { EndStepCard } from '@components/flow/EndStep/Card';
import { EndStepHero } from '@components/flow/EndStep/EndStepHero';
import { useContent } from '@sitecore/common';
import { PaymentArrangementEndStepRendering } from '@sitecore/types/PaymentArrangementEndStep';

export const EndStep = () => {
  const { fields } = useContent<PaymentArrangementEndStepRendering>();

  const {
    card: { title, content, cta },
    image,
  } = fields;

  console.log({ image });

  return (
    <EndStepHero heroImage={{ value: { src: `${getStaticPrefix()}/images/${label}/meterreadings/payment.png` } }}>
      <EndStepCard
        title={title}
        content={content}
        buttonHref={cta.value.href}
        buttonText={cta.value.text}></EndStepCard>
    </EndStepHero>
  );
};
