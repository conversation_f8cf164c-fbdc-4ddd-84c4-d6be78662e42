import { useEffect } from 'react';

import { getStaticPrefix } from '@common/env';
import { EndStepCard } from '@components/flow/EndStep/Card';
import { EndStepHero } from '@components/flow/EndStep/EndStepHero';
import useDC from '@dc/useDC';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { PaymentArrangementEndStepRendering } from '@sitecore/types/PaymentArrangementEndStep';
import { Box, Grid } from '@sparky';
import { useTracking } from '@tracking';

import { Steps } from '../Router/Router';

export const EndStep = () => {
  const { fields } = useContent<PaymentArrangementEndStepRendering>();
  const { trackFunnelCompleted, trackPageView } = useTracking();
  const { activePath } = useRouter();
  const { label } = useDC();

  // const {
  //   card: { title, content, cta },
  // } = fields;

  useEffect(() => {
    trackFunnelCompleted({ step: Steps.OVERVIEW_COMPLETE });
  }, [activePath, trackFunnelCompleted, trackPageView]);

  return (
    <Box backgroundColor={'backgroundVarSix'}>
      <Grid alignX="center" alignY="center" gridTemplateRows="1fr auto auto auto 1fr" minHeight="100vh">
        {/* Content centered in rows 2-4 */}
        <Grid.Item gridRow="2/5">
          <EndStepHero heroImage={{ value: { src: `${getStaticPrefix()}/images/${label}/meterreadings/payment.png` } }}>
            <EndStepCard
              title={'title'}
              content={'content'}
              buttonHref={'cta.value.href'}
              buttonText={'cta.value.text'}
            />
          </EndStepHero>
        </Grid.Item>
      </Grid>
    </Box>
  );
};
