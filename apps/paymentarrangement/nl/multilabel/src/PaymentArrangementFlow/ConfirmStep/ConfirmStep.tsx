import { useCallback, useContext, FC, useMemo } from 'react';

import RichText from '@components/RichText/RichText';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import ComponentError from '@components/ui/ComponentError/ComponentError';
import { useFormatter } from '@i18n';
import { Financials_PaymentArrangement_PaymentArrangementResponseModel } from '@monorepo-types/dc';
import { usePlaceholderContent } from '@sitecore/common';
import { PaymentArrangementConfirmStepRendering } from '@sitecore/types/PaymentArrangementConfirmStep';
import { Card, Stack, Box, Heading, TextLink, Button, Text, Bucket } from '@sparky';

import { InvoicesListItem } from '../../components/InvoicesListItem/InvoicesListItem';
import { InvoiceTypeLabels } from '../../hooks/useInvoiceMetaData';
import { PaymentArrangementFlowMachine } from '../state/paymentArrangementFlow.machine';
import { TermListItem } from '../TermListItem/TermListItem';

export const ConfirmStep: FC = () => {
  const { state } = useContext(PaymentArrangementFlowMachine);

  if (!state.context.paymentArrangementProposal) return <ComponentError />;

  return <ConfirmStepComponent {...state.context.paymentArrangementProposal} />;
};

export const ConfirmStepComponent: FC<Financials_PaymentArrangement_PaymentArrangementResponseModel> = ({
  terms,
  outstandingInvoices,
  firstTermRange,
}) => {
  const { PaymentArrangementConfirmStep } = usePlaceholderContent<{
    PaymentArrangementConfirmStep: PaymentArrangementConfirmStepRendering;
  }>();
  const { fields } = PaymentArrangementConfirmStep;

  const { state, send } = useContext(PaymentArrangementFlowMachine);
  const { date, currency, format } = useFormatter();
  const isDirectDebit = state.context.financialOverview?.paymentMethodIsDirectDebit;

  const totalAmount = outstandingInvoices?.reduce((amount, invoice) => amount + invoice.outstandingAmount, 0) || 0;

  const onCreatePaymentArrangement = useCallback(() => {
    send({ type: 'CREATE_PAYMENT_ARRANGEMENT' });
  }, [send]);

  const invoiceTypeLabels: InvoiceTypeLabels = useMemo(
    () => ({
      NoInvoice: fields.invoiceType.invoiceTypeNoInvoiceLabel.value,
      Yearnote: fields.invoiceType.invoiceTypeYearnoteLabel.value,
      AdvancePaymentInvoice: fields.invoiceType.invoiceTypeAdvancePaymentInvoiceLabel.value,
      EndOfAgreementInvoice: fields.invoiceType.invoiceTypeEndOfAgreementInvoiceLabel.value,
      CorrectionInvoice: fields.invoiceType.invoiceTypeCorrectionInvoiceLabel.value,
      SingleCostInvoice: fields.invoiceType.invoiceTypeSingleCostInvoiceLabel.value,
      AdditionalInvoice: fields.invoiceType.invoiceTypeAdditionalInvoiceLabel.value,
      InterimInvoice: fields.invoiceType.invoiceTypeInterimInvoiceLabel.value,
      AdvancePaymentSingleCostInvoice: fields.invoiceType.invoiceTypeAdvancePaymentSingleCostInvoiceLabel.value,
      OtherInvoiceType: fields.invoiceType.invoiceTypeOtherInvoiceTypeLabel.value,
      DunningInvoice: fields.invoiceType.invoiceTypeDunningInvoiceLabel.value,
    }),
    [fields.invoiceType],
  );

  return (
    <Stack gap="6">
      <Stack gap="2">
        <Heading as="h1" size="M">
          {fields.header.title.value}
        </Heading>
        <RichText html={fields.content.content.value} />
      </Stack>
      <Stack.Item>
        <Card>
          <Box padding="6">
            <Stack gap="3">
              <Stack.Item>
                <Heading as="h3" size="2XS">
                  {format(fields.paymentArrangementCard.title.value, { totalAmount: currency.euro(totalAmount) })}
                </Heading>
                <RichText
                  html={format(fields.paymentArrangementCard.description.value, {
                    termsamount: state.context.termsamount || '',
                    totalAmount: currency.euro(totalAmount) || '',
                  })}
                />
              </Stack.Item>
              {terms?.map(({ amount, dueDate }, index) => (
                <TermListItem
                  key={index}
                  amount={currency.euro(amount)}
                  date={format(
                    isDirectDebit
                      ? fields.termInterval.directDebitDescription.value
                      : fields.termInterval.description.value,
                    {
                      date: date.long(dueDate),
                    },
                  )}
                  title={format(fields.termInterval.title.value, { interval: index + 1 })}
                  isFirstItem={index === 0}
                />
              ))}

              <TextLink color="textLowEmphasis" onClick={() => send({ type: 'GOTO_STEP', target: 'SELECT_TERMS' })}>
                {fields.footer.changeButtonText.value}
              </TextLink>
            </Stack>
          </Box>
        </Card>
      </Stack.Item>
      <Stack.Item>
        <Card>
          <Box padding="6">
            <Stack gap="1">
              <Heading as="h3" size="2XS">
                {fields.startDateCard.title.value}
              </Heading>
              <RichText
                html={format(
                  isDirectDebit ? fields.startDateCard.directDebitContent.value : fields.startDateCard.content.value,
                  {
                    date: date.long(firstTermRange?.end || ''),
                  },
                )}
              />
              <Stack.Item>
                <TextLink
                  color="textLowEmphasis"
                  onClick={() => send({ type: 'GOTO_STEP', target: 'EXPIRATION_DATE_FIRST_TERM' })}>
                  {fields.footer.changeButtonText.value}
                </TextLink>
              </Stack.Item>
            </Stack>
          </Box>
        </Card>
      </Stack.Item>
      <Stack.Item>
        <Bucket title={fields.invoicesCard.title.value}>
          <Box padding="6">
            <Stack gap="3">
              {outstandingInvoices?.map((invoice, index) => (
                <InvoicesListItem
                  key={invoice.id}
                  invoice={invoice}
                  isFirstItem={index === 0}
                  invoiceTypeLabels={invoiceTypeLabels}
                />
              ))}
              <TextLink color="textLowEmphasis" onClick={() => send({ type: 'GOTO_STEP', target: 'SELECT_INVOICES' })}>
                {fields.footer.changeButtonText.value}
              </TextLink>
            </Stack>
          </Box>
        </Bucket>
      </Stack.Item>
      <Stack.Item>
        <TrackedNotificationBox
          variant="info"
          label={fields.infoNotification.label.value}
          text={
            <Text>
              <RichText html={fields.infoNotification.description.value} />
            </Text>
          }
          isAlert={false}
        />
      </Stack.Item>
      <Stack.Item>
        <Button type="submit" isLoading={state.matches({ OVERVIEW: 'LOADING' })} onClick={onCreatePaymentArrangement}>
          {fields.footer.nextButtonText.value}
        </Button>
      </Stack.Item>
      {state.matches({ OVERVIEW: 'ERROR' }) && (
        <Stack.Item>
          <TrackedNotificationBox
            variant="error"
            label={fields.formFeedback.errorLabel.value}
            text={<RichText html={fields.formFeedback.errorText.value} />}
            isAlert={true}
          />
        </Stack.Item>
      )}
    </Stack>
  );
};
