import { FC, PropsWithChildren, useContext, useEffect } from 'react';

import { RouterProvider, Routes, Route, useRouter } from '@dxp-next';
import { usePlaceholderContent } from '@sitecore/common';
import { PaymentArrangementHeaderRendering } from '@sitecore/types/PaymentArrangementHeader';
import { Skeleton } from '@sparky';
import { useTracking } from '@tracking';

import { ConfirmStep } from '../ConfirmStep/ConfirmStep';
import { EndStep } from '../EndStep/EndStep';
import { InvoicesStep } from '../InvoicesStep/InvoicesStep';
import { StartDateStep } from '../StartDateStep/StartDateStep';
import { PaymentArrangementFlowMachine } from '../state/paymentArrangementFlow.machine';
import { StatesWithRoute } from '../state/paymentArrangementFlow.types';
import { TermsStep } from '../TermsStep/TermsStep';

const PaymentArrangementRoutes: Record<StatesWithRoute, string> = {
  SELECT_INVOICES: '/',
  SELECT_TERMS: '/termijnen/',
  EXPIRATION_DATE_FIRST_TERM: '/startdatum/',
  OVERVIEW: '/bevestig/',
  THANK_YOU: '/bedankt/',
};

export enum Steps {
  SELECT_INVOICES = 'Nota - start',
  SELECT_TERMS = 'Termijnen',
  EXPIRATION_DATE_FIRST_TERM = 'Startdatum',
  OVERVIEW = 'Bevestig',
  OVERVIEW_COMPLETE = 'Bevestig - complete',
}

export const StateRouter: FC<PropsWithChildren> = ({ children }) => {
  const { trackFunnelStep } = useTracking();
  const router = useRouter();
  const { state } = useContext(PaymentArrangementFlowMachine);
  const { PaymentArrangementHeader } = usePlaceholderContent<{
    PaymentArrangementHeader: PaymentArrangementHeaderRendering;
  }>();
  const { fields } = PaymentArrangementHeader;

  const [, stateRoute] =
    (Object.entries(PaymentArrangementRoutes) as [StatesWithRoute, string][]).find(([paymentArrangementState]) =>
      state.matches(paymentArrangementState),
    ) || [];

  const isCurrentStateRoute = router.activeRoute === stateRoute;

  const [, paymentArrangementStateStep] =
    (Object.entries(Steps) as [StatesWithRoute, string][]).find(([paymentArrangementState]) =>
      state.matches(paymentArrangementState),
    ) || [];

  useEffect(() => {
    if (!paymentArrangementStateStep) return;

    const funnelStep = paymentArrangementStateStep;

    if (
      state.matches({ SELECT_TERMS: 'PENDING' }) ||
      state.matches({ EXPIRATION_DATE_FIRST_TERM: 'PENDING' }) ||
      state.matches({ OVERVIEW: 'PENDING' })
    ) {
      trackFunnelStep({ step: funnelStep });
    }
  }, [paymentArrangementStateStep, state, trackFunnelStep]);

  useEffect(() => {
    if (state.matches('BLOCKED')) {
      router.pushHref(fields.paymentArrangementOverview.link.value.href);
      return;
    }
    if (stateRoute && !isCurrentStateRoute) router.push(stateRoute);
  }, [fields.paymentArrangementOverview.link.value.href, isCurrentStateRoute, router, state, stateRoute]);

  if (!isCurrentStateRoute) return <Skeleton height={400} />;

  return <>{children}</>;
};

export const PaymentArrangementRouter: FC = () => (
  <RouterProvider>
    <StateRouter>
      <Routes>
        <Route route={PaymentArrangementRoutes.SELECT_INVOICES} component={InvoicesStep} />
        <Route route={PaymentArrangementRoutes.SELECT_TERMS} component={TermsStep} />
        <Route route={PaymentArrangementRoutes.EXPIRATION_DATE_FIRST_TERM} component={StartDateStep} />
        <Route route={PaymentArrangementRoutes.OVERVIEW} component={ConfirmStep} />
        <Route route={PaymentArrangementRoutes.THANK_YOU} component={EndStep} />
      </Routes>
    </StateRouter>
  </RouterProvider>
);
