import { FC, useContext } from 'react';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import useDC from '@dc/useDC';
import { Link, useRouter } from '@dxp-next';
import { usePlaceholderContent } from '@sitecore/common';
import { PaymentArrangementHeaderRendering } from '@sitecore/types/PaymentArrangementHeader';
import {
  Box,
  Button,
  Divider,
  EnecoLogo,
  Grid,
  IconButton,
  NavLink,
  OxxioLogo,
  PageGrid,
  ProgressIndicator,
  Stack,
  WoonenergieLogo,
} from '@sparky';
import { ChevronLeftIcon, CloseIcon } from '@sparky/icons';

import { PaymentArrangementFlowMachine } from '../state/paymentArrangementFlow.machine';
import { StatesWithRoute, StepValue } from '../state/paymentArrangementFlow.types';

const labels = {
  eneco: <EnecoLogo />,
  oxxio: <OxxioLogo />,
  woonenergie: <WoonenergieLogo />,
};

export type HeaderPlaceholder = {
  PaymentArrangementHeader: PaymentArrangementHeaderRendering;
};

export const Header: FC = () => {
  const router = useRouter();
  const { label } = useDC();
  const { state, send } = useContext(PaymentArrangementFlowMachine);
  const { PaymentArrangementHeader } = usePlaceholderContent<HeaderPlaceholder>();
  const { fields } = PaymentArrangementHeader;

  const steps: StepValue[] = state.context?.steps;
  const showPreviousButton = steps.findIndex(step => state.matches(step.state)) > 0;

  const title: Partial<Record<StatesWithRoute, string>> = {
    SELECT_INVOICES: fields.select.invoicesStepTitle.value,
    SELECT_TERMS: fields.select.termsStepTitle.value,
    EXPIRATION_DATE_FIRST_TERM: fields.select.expirationDateStepTitle.value,
    OVERVIEW: fields.proposalOverviewStep.title.value,
  };

  const onConfirm = (link: string) => router.push(link);

  const logoLink = fields.header.logoLink.value.href;

  const flowReachedFinalState = state.matches('THANK_YOU');

  return (
    <Box backgroundColor="backgroundPrimary">
      <PageGrid>
        <PageGrid.Item gridColumn="1/-1">
          <Grid alignY="center" columns="3">
            <Grid.Item>
              {showPreviousButton && (
                <NavLink
                  onClick={() => send({ type: 'PREVIOUS_STEP' })}
                  variant="secondary"
                  leftIcon={<ChevronLeftIcon />}>
                  {fields.footer.previousButtonText.value}
                </NavLink>
              )}
            </Grid.Item>
            <Grid.Item>
              <Stack alignX="center">
                <Box paddingTop="3" paddingBottom="2">
                  {logoLink && (
                    <Link href={logoLink} linkType={fields.header.logoLink.value.linktype}>
                      <a>{labels[label]}</a>
                    </Link>
                  )}
                </Box>
              </Stack>
            </Grid.Item>
            <Grid.Item>
              <Stack alignX="end">
                <TrackedDialog
                  trigger={
                    <IconButton label={fields.closeDialog.title.value}>
                      <CloseIcon />
                    </IconButton>
                  }
                  title={fields.closeDialog.title.value}
                  description={<RichText html={fields.closeDialog.description.value} />}>
                  <Stack direction={{ initial: 'column', md: 'row' }} gap="5">
                    <Button onClick={() => onConfirm(fields.closeDialog.confirmLink.value.href)} action="primary">
                      {fields.closeDialog.confirmLink.value.text}
                    </Button>
                    <TrackedDialog.Close asChild>
                      <Button action="secondary">{fields.closeDialog.cancelButtonText.value}</Button>
                    </TrackedDialog.Close>
                  </Stack>
                </TrackedDialog>
              </Stack>
            </Grid.Item>
          </Grid>
        </PageGrid.Item>
      </PageGrid>

      <Divider />
      {!flowReachedFinalState && (
        <PageGrid>
          <PageGrid.Item gridColumn={{ initial: '1/-1', md: '4/-1', lg: '4/10' }}>
            <ProgressIndicator>
              {steps.map(({ state, isActive, isDisabled }) => (
                <ProgressIndicator.Item
                  key={state}
                  isActive={isActive}
                  isDisabled={isDisabled}
                  onClick={() => send({ type: 'GOTO_STEP', target: state })}>
                  {title[state] || ''}
                </ProgressIndicator.Item>
              ))}
            </ProgressIndicator>
          </PageGrid.Item>
        </PageGrid>
      )}
    </Box>
  );
};
