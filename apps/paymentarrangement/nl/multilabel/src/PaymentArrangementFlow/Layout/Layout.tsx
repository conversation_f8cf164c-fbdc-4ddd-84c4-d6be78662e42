import { FC, useContext } from 'react';

import { getStateValueStrings } from '@common/xstate/stateValue';
import { StackContainer } from '@custom-components/selfservice';
import useDC from '@dc/useDC';
import { DC_Repositories_Base_Enumerations_Label } from '@monorepo-types/dc';
import { Box, SkipToMain, PageGrid, Stack } from '@sparky';
import { BoxProps } from '@sparky/types';

import { Header } from '../Header/Header';
import { PaymentArrangementRouter } from '../Router/Router';
import { PaymentArrangementFlowMachine } from '../state/paymentArrangementFlow.machine';

const backgroundColors: Record<
  string,
  Partial<Record<DC_Repositories_Base_Enumerations_Label, BoxProps['backgroundColor']>>
> = {
  THANK_YOU: {
    eneco: 'backgroundVarOne',
    oxxio: 'backgroundCMSVarThree',
  },
};

export const Layout: FC = () => {
  const { label } = useDC();
  const { state } = useContext(PaymentArrangementFlowMachine);

  const stateValues = getStateValueStrings(state.value);

  const [_, backgroundColor] = Object.entries(backgroundColors).find(([key]) => stateValues.includes(key)) || [];
  const color = backgroundColor?.[label] || 'backgroundSecondary';

  const flowReachedFinalState = state.matches('THANK_YOU') || state.matches('ERROR_PAGE');

  if (!flowReachedFinalState) {
    return (
      <Stack gap={'16'}>
        <Header />
        <PageGrid>
          <PageGrid.Item gridColumn={{ initial: '1/-1', md: '4/-1', lg: '4/10' }}>
            <PaymentArrangementRouter />
          </PageGrid.Item>
        </PageGrid>
      </Stack>
    );
  }

  return (
    <Box backgroundColor={color}>
      <StackContainer>
        <Box paddingBottom="10">
          <SkipToMain.SkipLink />
          <SkipToMain.Main>
            <Header />
            <PaymentArrangementRouter />
          </SkipToMain.Main>
        </Box>
      </StackContainer>
    </Box>
  );
};
