import { FC, useMemo } from 'react';

import ComponentError from '@components/ui/ComponentError/ComponentError';
import useDC from '@dc/useDC';
import { useMachine } from '@eneco-packages/xstate-custom/xstate-react';

import { Layout } from './Layout/Layout';
import {
  PaymentArrangementFlowMachine,
  getPaymentArrangementFlowMachine,
} from './state/paymentArrangementFlow.machine';
import { CustomerContext } from './state/paymentArrangementFlow.types';

const PaymentArrangementFlow: FC = () => {
  const { customerId, accountId, businessUnit, label } = useDC();

  if (!customerId || !accountId) return <ComponentError />;

  return (
    <PaymentArrangementFlowComponent
      context={{
        customerId,
        accountId,
        businessUnit,
        label,
      }}
    />
  );
};

export const PaymentArrangementFlowComponent: FC<{ context: CustomerContext }> = ({ context }) => {
  const machine = useMemo(() => getPaymentArrangementFlowMachine(context), [context]);
  const [state, send] = useMachine(machine);

  return (
    <PaymentArrangementFlowMachine.Provider value={{ state, send }}>
      <Layout />
    </PaymentArrangementFlowMachine.Provider>
  );
};

export default PaymentArrangementFlow;
