import { AuthenticationContext } from '@dc/provider';
import {
  Financials_FinancialOverviewResponse,
  Financials_PaymentArrangementsRequest,
  Financials_PaymentArrangement_PaymentArrangementResponseModel,
} from '@monorepo-types/dc';

type CustomerContextValues = Pick<
  NonNullable<AuthenticationContext>,
  'accountId' | 'businessUnit' | 'customerId' | 'label'
>;

type VALIDATE = {
  value: { VALIDATE: 'GET_FINANCIAL_OVERVIEW' | 'GET_PAYMENT_ARRANGEMENT' };
  context: Context;
};

type STATE_LOADING_VALUE = 'PENDING' | 'LOADING' | 'ERROR' | 'SUCCESS';

type SELECT_INVOICES = {
  value: { SELECT_INVOICES: STATE_LOADING_VALUE } | 'SELECT_INVOICES';
  context: Context;
};

type SELECT_TERMS = {
  value: { SELECT_TERMS: STATE_LOADING_VALUE } | 'SELECT_TERMS';
  context: Context;
};

type EXPIRATION_DATE_FIRST_TERM = {
  value: { EXPIRATION_DATE_FIRST_TERM: STATE_LOADING_VALUE } | 'EXPIRATION_DATE_FIRST_TERM';
  context: Context;
};

type OVERVIEW = {
  value: { OVERVIEW: STATE_LOADING_VALUE } | 'OVERVIEW';
  context: Context;
};

type THANK_YOU = { value: 'THANK_YOU'; context: Context };

type CONFIRMED = { value: 'CONFIRMED'; context: Context };

type BLOCKED = { value: 'BLOCKED'; context: Context };

export type CustomerContext = {
  [P in keyof CustomerContextValues]: NonNullable<CustomerContextValues[P]>;
};

export type StepValue = {
  state: StatesWithRoute;
  isDisabled: boolean;
  isActive: boolean;
};

export type Context = CustomerContext &
  Financials_PaymentArrangementsRequest & {
    financialOverview: Financials_FinancialOverviewResponse | null;
    paymentArrangementProposal: Financials_PaymentArrangement_PaymentArrangementResponseModel | null;
    steps: StepValue[];
  };

export type State =
  | VALIDATE
  | SELECT_INVOICES
  | SELECT_TERMS
  | EXPIRATION_DATE_FIRST_TERM
  | OVERVIEW
  | CONFIRMED
  | THANK_YOU
  | BLOCKED;

export type StateValue = State['value'];
export type StatesWithRoute = Extract<Exclude<State['value'], CONFIRMED['value'] | BLOCKED['value']>, string>;

export type PROPOSE_PAYMENT_ARRANGEMENT_EVENT = {
  type: 'PROPOSE_PAYMENT_ARRANGEMENT';
} & Financials_PaymentArrangementsRequest;

export type UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL_EVENT = {
  type: 'UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL';
  output: Financials_PaymentArrangement_PaymentArrangementResponseModel;
};

export type GOTO_STEP_EVENT = {
  type: 'GOTO_STEP';
  target: State['value'];
};

export type NEXT_STEP_EVENT = {
  type: 'NEXT_STEP';
  termsamount?: number;
};

export type PREVIOUS_STEP_EVENT = {
  type: 'PREVIOUS_STEP';
};

export type CREATE_PAYMENT_ARRANGEMENT_EVENT = {
  type: 'CREATE_PAYMENT_ARRANGEMENT';
};

export type Event =
  | NEXT_STEP_EVENT
  | PREVIOUS_STEP_EVENT
  | CREATE_PAYMENT_ARRANGEMENT_EVENT
  | PROPOSE_PAYMENT_ARRANGEMENT_EVENT
  | UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL_EVENT
  | GOTO_STEP_EVENT;
