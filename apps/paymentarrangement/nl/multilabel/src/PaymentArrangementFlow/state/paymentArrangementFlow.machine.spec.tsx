import { useMemo } from 'react';

import { render, waitFor } from '@testing-library/react';

import { useMachine } from '@eneco-packages/xstate-custom/xstate-react';

import { getPaymentArrangementFlowMachine } from './paymentArrangementFlow.machine';
import { CustomerContext } from './paymentArrangementFlow.types';

const defaultCustomerContext: CustomerContext = {
  accountId: 1,
  customerId: 2029587,
  label: 'eneco',
  businessUnit: 'nl',
};

const TestComponent = ({ customerId, listener }: { customerId: number; listener: (state: unknown) => void }) => {
  const machine = useMemo(
    () =>
      getPaymentArrangementFlowMachine({
        accountId: 1,
        label: 'eneco',
        businessUnit: 'nl',
        customerId,
      }),
    [customerId],
  );
  const [state] = useMachine(machine);

  listener(state);

  return <></>;
};

describe('PaymentArrangementFlowMachine', () => {
  it('should go to state SELECT_INVOICES', async () => {
    const stateListener = jest.fn();
    render(<TestComponent customerId={2029587} listener={stateListener} />);

    await waitFor(() =>
      expect(stateListener).toHaveBeenCalledWith(
        expect.objectContaining({
          value: { SELECT_INVOICES: 'PENDING' },
        }),
      ),
    );
  });

  it('should go to state BLOCKED, paymentArrangementIsPossible is false', async () => {
    const stateListener = jest.fn();
    render(<TestComponent customerId={2008547} listener={stateListener} />);

    await waitFor(() =>
      expect(stateListener).toHaveBeenCalledWith(
        expect.objectContaining({
          value: 'BLOCKED',
        }),
      ),
    );
  });

  it('should go to state BLOCKED, paymentArrangement exists', async () => {
    const stateListener = jest.fn();
    render(<TestComponent customerId={2008545} listener={stateListener} />);

    await waitFor(() =>
      expect(stateListener).toHaveBeenCalledWith(
        expect.objectContaining({
          value: 'BLOCKED',
        }),
      ),
    );
  });

  it('should go to state BLOCKED, invalid paymentArrangement exists', async () => {
    const stateListener = jest.fn();
    render(<TestComponent customerId={2008548} listener={stateListener} />);

    await waitFor(() =>
      expect(stateListener).toHaveBeenCalledWith(
        expect.objectContaining({
          value: 'BLOCKED',
        }),
      ),
    );
  });

  describe('THANK_YOU page transitions', () => {
    it('should have THANK_YOU as a final state', () => {
      const machine = getPaymentArrangementFlowMachine(defaultCustomerContext);
      const machineDefinition = machine.config;

      // Verify THANK_YOU state exists and is final
      const thankYouState = machineDefinition.states?.THANK_YOU;
      expect(thankYouState).toBeDefined();
      expect(thankYouState?.type).toBe('final');
      expect(thankYouState?.id).toBe('THANK_YOU');
    });

    it('should configure OVERVIEW.LOADING to transition to THANK_YOU on success', () => {
      const machine = getPaymentArrangementFlowMachine(defaultCustomerContext);
      const machineDefinition = machine.config;

      // Verify OVERVIEW.LOADING transitions to THANK_YOU on success
      const overviewStates = machineDefinition.states?.OVERVIEW?.states;
      const loadingState = overviewStates?.LOADING;
      const invokeConfig = Array.isArray(loadingState?.invoke) ? loadingState?.invoke[0] : loadingState?.invoke;

      expect(invokeConfig?.onDone?.target).toBe('#THANK_YOU');
    });
  });
});
