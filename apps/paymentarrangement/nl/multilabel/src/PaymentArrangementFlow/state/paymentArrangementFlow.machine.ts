import { createContext } from 'react';

import Logger from '@common/log';
import { Actor, MachineContext, assign, setup } from '@eneco-packages/xstate-custom/xstate';

import { actors } from './paymentArrangementFlow.actors';
import { CustomerContext, Context, Event } from './paymentArrangementFlow.types';
import { isProposePaymentArrangementEvent, isUpdatePaymentProposalEvent } from '../utils';

type ReturnTypeOfCreateMachine = ReturnType<typeof getPaymentArrangementFlowMachine>;

export const PaymentArrangementFlowMachine = createContext(
  {} as {
    state: MachineContext;
    send: Actor<ReturnTypeOfCreateMachine>['send'];
  },
);

const enableStep = (stateValue: string) => {
  return ({ context }: { context: Context }) =>
    context.steps.map((step, index, steps) => ({
      ...step,
      isActive: step.state === stateValue,
      isDisabled: steps.findIndex(({ state }) => state === stateValue) < index,
    }));
};

export const getPaymentArrangementFlowMachine = ({ accountId, businessUnit, customerId, label }: CustomerContext) =>
  setup({
    types: {
      context: {} as Context,
      events: {} as Event,
    },
    actions: {
      PROPOSE_PAYMENT_ARRANGEMENT: assign({
        termsamount: ({ context, event }) => {
          if (isProposePaymentArrangementEvent(event)) {
            return event?.termsamount ?? context.termsamount;
          }
          return context.termsamount;
        },
        expirationdatefirstterm: ({ context, event }) => {
          if (isProposePaymentArrangementEvent(event)) {
            return event?.expirationdatefirstterm ?? context.expirationdatefirstterm;
          }
          return context.expirationdatefirstterm;
        },
        excludedinvoices: ({ context, event }) => {
          if (isProposePaymentArrangementEvent(event)) {
            return event?.excludedinvoices ?? context.excludedinvoices;
          }
          return context.excludedinvoices;
        },
      }),
      UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL: assign({
        paymentArrangementProposal: ({ context, event }) => {
          if (isUpdatePaymentProposalEvent(event)) {
            const data = event.output;
            return {
              ...context.paymentArrangementProposal,
              ...data,
              termOptions: data?.termOptions?.length
                ? data.termOptions
                : context.paymentArrangementProposal?.termOptions,
            };
          }
          return context.paymentArrangementProposal;
        },
      }),
    },

    actors,
  }).createMachine({
    id: 'paymentArrangementMachine',
    context: {
      accountId,
      businessUnit,
      customerId,
      label,
      termsamount: null,
      expirationdatefirstterm: null,
      excludedinvoices: null,
      paymentArrangementProposal: null,
      steps: [
        {
          state: 'SELECT_INVOICES',
          isActive: false,
          isDisabled: true,
        },
        {
          state: 'SELECT_TERMS',
          isActive: false,
          isDisabled: true,
        },
        {
          state: 'EXPIRATION_DATE_FIRST_TERM',
          isActive: false,
          isDisabled: true,
        },
        {
          state: 'OVERVIEW',
          isActive: false,
          isDisabled: true,
        },
      ],
      financialOverview: null,
    },
    initial: 'VALIDATE',
    states: {
      VALIDATE: {
        initial: 'GET_FINANCIAL_OVERVIEW',
        states: {
          GET_FINANCIAL_OVERVIEW: {
            invoke: {
              id: 'GET_FINANCIAL_OVERVIEW',
              src: 'GET_FINANCIAL_OVERVIEW',
              input: ({ context }) => context,
              onDone: [
                {
                  guard: ({ event }) => event?.output?.paymentArrangementIsPossible ?? false,
                  actions: assign({
                    financialOverview: ({ event, context }) => event?.output ?? context.financialOverview,
                  }),
                  target: 'GET_PAYMENT_ARRANGEMENT',
                },
                { target: '#BLOCKED' },
              ],
              onError: {
                target: '#BLOCKED',
                actions: ({ event }) => Logger.error('03Iz4W', `GET_FINANCIAL_OVERVIEW`, event),
              },
            },
          },
          GET_PAYMENT_ARRANGEMENT: {
            invoke: {
              id: 'GET_PAYMENT_ARRANGEMENT',
              src: 'GET_PAYMENT_ARRANGEMENT',
              input: ({ context }) => context,
              onDone: [
                {
                  guard: ({ event }) => {
                    if (event.output) {
                      return event?.output?.length === 0;
                    }
                    return false;
                  },
                  target: '#SELECT_INVOICES',
                },
                { target: '#BLOCKED' },
              ],
              onError: {
                target: '#BLOCKED',
                actions: event => Logger.error('mVXn1j', `Unexpected error for GET_PAYMENT_ARRANGEMENT`, event),
              },
            },
          },
        },
      },
      SELECT_INVOICES: {
        entry: assign({ steps: enableStep('SELECT_INVOICES') }),
        id: 'SELECT_INVOICES',
        initial: 'PENDING',
        on: {
          PROPOSE_PAYMENT_ARRANGEMENT: {
            actions: 'PROPOSE_PAYMENT_ARRANGEMENT',
            target: 'SELECT_INVOICES.LOADING',
          },
        },
        states: {
          PENDING: {},
          ERROR: {},
          LOADING: {
            invoke: {
              id: 'PROPOSE_PAYMENT_ARRANGEMENT',
              src: 'PROPOSE_PAYMENT_ARRANGEMENT',
              input: ({ context }) => context,
              onDone: {
                target: 'SUCCESS',
                actions: 'UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL',
              },
              onError: {
                target: 'ERROR',
                actions: ({ event }) =>
                  Logger.error('qe1h3n', `Unexpected error for PROPOSE_PAYMENT_ARRANGEMENT`, event),
              },
            },
          },
          SUCCESS: {
            type: 'final',
          },
        },
        onDone: 'SELECT_TERMS',
      },
      SELECT_TERMS: {
        entry: assign({ steps: enableStep('SELECT_TERMS') }),
        id: 'SELECT_TERMS',
        on: {
          NEXT_STEP: {
            actions: 'PROPOSE_PAYMENT_ARRANGEMENT',
            target: 'EXPIRATION_DATE_FIRST_TERM',
          },
          PROPOSE_PAYMENT_ARRANGEMENT: {
            actions: 'PROPOSE_PAYMENT_ARRANGEMENT',
            target: 'SELECT_TERMS.LOADING',
          },
          PREVIOUS_STEP: {
            target: 'SELECT_INVOICES',
          },
          GOTO_STEP: [
            {
              guard: ({ event }) => event.target === 'SELECT_INVOICES',
              target: 'SELECT_INVOICES',
            },
          ],
        },
        initial: 'PENDING',
        states: {
          PENDING: {},
          ERROR: {},
          LOADING: {
            invoke: {
              id: 'PROPOSE_PAYMENT_ARRANGEMENT',
              src: 'PROPOSE_PAYMENT_ARRANGEMENT',
              input: ({ context }) => context,
              onDone: {
                target: 'SUCCESS',
                actions: 'UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL',
              },
              onError: {
                target: 'ERROR',
                actions: ({ event }) =>
                  Logger.error('DsOMSS', `Unexpected error for PROPOSE_PAYMENT_ARRANGEMENT`, event),
              },
            },
          },
          SUCCESS: {},
        },
      },
      EXPIRATION_DATE_FIRST_TERM: {
        entry: assign({ steps: enableStep('EXPIRATION_DATE_FIRST_TERM') }),
        id: 'EXPIRATION_DATE_FIRST_TERM',
        on: {
          PREVIOUS_STEP: {
            target: 'SELECT_TERMS',
          },
          PROPOSE_PAYMENT_ARRANGEMENT: {
            actions: 'PROPOSE_PAYMENT_ARRANGEMENT',
            target: 'EXPIRATION_DATE_FIRST_TERM.LOADING',
          },
          GOTO_STEP: [
            {
              guard: ({ event }) => event.target === 'SELECT_TERMS',
              target: 'SELECT_TERMS',
            },
            {
              guard: ({ event }) => event.target === 'SELECT_INVOICES',
              target: 'SELECT_INVOICES',
            },
          ],
        },
        initial: 'PENDING',
        states: {
          PENDING: {},
          ERROR: {},
          LOADING: {
            invoke: {
              id: 'PROPOSE_PAYMENT_ARRANGEMENT',
              src: 'PROPOSE_PAYMENT_ARRANGEMENT',
              input: ({ context }) => context,
              onDone: {
                target: 'SUCCESS',
                actions: 'UPDATE_PAYMENT_ARRANGEMENT_PROPOSAL',
              },
              onError: {
                target: 'ERROR',
                actions: ({ event }) =>
                  Logger.error('0CimHX', `Unexpected error for PROPOSE_PAYMENT_ARRANGEMENT`, event),
              },
            },
          },
          SUCCESS: {
            type: 'final',
          },
        },
        onDone: 'OVERVIEW',
      },
      OVERVIEW: {
        entry: assign({ steps: enableStep('OVERVIEW') }),
        id: 'OVERVIEW',
        on: {
          PREVIOUS_STEP: {
            target: 'EXPIRATION_DATE_FIRST_TERM',
          },
          CREATE_PAYMENT_ARRANGEMENT: {
            target: 'OVERVIEW.LOADING',
          },
          GOTO_STEP: [
            {
              guard: ({ event }) => event.target === 'SELECT_TERMS',
              target: 'SELECT_TERMS',
            },
            {
              guard: ({ event }) => event.target === 'EXPIRATION_DATE_FIRST_TERM',
              target: 'EXPIRATION_DATE_FIRST_TERM',
            },
            {
              guard: ({ event }) => event.target === 'SELECT_INVOICES',
              target: 'SELECT_INVOICES',
            },
          ],
        },
        initial: 'PENDING',
        states: {
          PENDING: {},
          ERROR: {},
          LOADING: {
            invoke: {
              id: 'CREATE_PAYMENT_ARRANGEMENT',
              src: 'CREATE_PAYMENT_ARRANGEMENT',
              input: ({ context }) => context,
              onDone: {
                target: '#THANK_YOU',
              },
              onError: {
                actions: ({ event }) =>
                  Logger.error('LjnwUa', `Unexpected error for CREATE_PAYMENT_ARRANGEMENT`, event),
                target: 'ERROR',
              },
            },
          },
          SUCCESS: {
            target: '#THANK_YOU',
          },
        },
      },
      THANK_YOU: {
        id: 'THANK_YOU',
        type: 'final',
      },
      BLOCKED: {
        id: 'BLOCKED',
        type: 'final',
      },
    },
  });
