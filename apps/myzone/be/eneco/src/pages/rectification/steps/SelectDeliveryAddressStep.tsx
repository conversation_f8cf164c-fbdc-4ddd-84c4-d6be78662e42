import { useEffect, useState } from 'react';

import { Controller, useFormContext } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints } from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { Box, Button, Heading, NotificationBox, RadioGroup, RadioTile, Stack, Stretch, VisuallyHidden } from '@sparky';
import { NotificationBoxProps } from '@sparky/types';

import { RoundedSkeleton } from '../../../components/Loading/RoundedSkeleton';
import { formattedAddress } from '../../../utils/addresses/addressFormatter';
import { useRectification } from '../hooks/useRectification';
import { useRectificationAddressOptions } from '../hooks/useRectificationAdressOptions';
import { FormValuesRectification, RECTIFICATION_SCHEMA_FIELDS } from '../types/schema.types';
import { MainStepProps } from '../types/steps.types';

const SelectDeliveryAddressStep: React.FC<MainStepProps> = ({ fields, onNextStep, onPreviousStep }) => {
  const {
    path: { accountNumber, invoiceNumber },
    meterReadingsData,
    session,
  } = useRectification();
  const { control, watch, trigger, setValue, setError: setFormError } = useFormContext<FormValuesRectification>();

  const [error, setError] = useState<NotificationBoxProps | null>(null);
  const [shouldFetchServiceDeliveryPoints, setShouldFetchServiceDeliveryPoints] = useState<boolean>(false);

  const selectedAddress = watch(RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS);

  const { data: serviceDeliveryPointsData, error: serviceDeliveryPointsDataError } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierServiceDeliveryPoints,
    {
      path: {
        accountNumber,
        addressIdentifier: selectedAddress ?? '',
      },
    },
    shouldFetchServiceDeliveryPoints
      ? [`accounts/${accountNumber}/delivery-addresses/${selectedAddress}/service-delivery-points`]
      : null,
    session,
  );

  const { rectificationAddressOptions, isLoading: addressOptionsIsLoading } = useRectificationAddressOptions();

  useEffect(() => {
    if (addressOptionsIsLoading) return;
    if (rectificationAddressOptions?.addresses?.length === 1) {
      // Only 1 address means you can skip this page.
      setValue(RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS, rectificationAddressOptions.addresses[0]?.id ?? '');
      onNextStep();
    }
  }, [addressOptionsIsLoading, onNextStep, rectificationAddressOptions?.addresses, setValue]);

  const onSubmit = async () => {
    const isValid = await trigger([RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS]);
    if (!isValid || !accountNumber || !invoiceNumber) return;
    setShouldFetchServiceDeliveryPoints(true);
  };

  useEffect(() => {
    if (!shouldFetchServiceDeliveryPoints || !serviceDeliveryPointsData) return;
    if (serviceDeliveryPointsDataError) {
      setError({
        isAlert: false,
        text: fields.summaryStep.errorNotification.value.content,
        title: fields.summaryStep.errorNotification.value.title,
        variant: 'error',
      });
      return;
    }
    const serviceDeliveryPoints = unwrapData(serviceDeliveryPointsData);

    const hasOverlap = (meterReadingsData ?? []).some(meter =>
      serviceDeliveryPoints?.serviceDeliveryPoints?.some(sdp => sdp.ean === meter.ean),
    );

    if (hasOverlap) {
      setFormError(RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS, { message: undefined });
      onNextStep();
    } else
      setFormError(RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS, {
        message: fields?.validationMessages?.addressValidationText?.value,
      });

    setShouldFetchServiceDeliveryPoints(false);
  }, [
    fields.summaryStep.errorNotification.value.content,
    fields.summaryStep.errorNotification.value.title,
    fields?.validationMessages?.addressValidationText?.value,
    meterReadingsData,
    onNextStep,
    serviceDeliveryPointsData,
    serviceDeliveryPointsDataError,
    setFormError,
    shouldFetchServiceDeliveryPoints,
  ]);

  return (
    <Box paddingTop="12">
      <Stack gap="10">
        <Stack.Item>
          <Stack gap="4">
            <Heading size="M" as="h1">
              {fields.chooseConnectionAddressStep?.title.value}
            </Heading>
            <RichText html={fields.chooseConnectionAddressStep.subTitle.value} />
          </Stack>
        </Stack.Item>
        <Stack.Item>
          <VisuallyHidden>
            <label id="RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS">
              {fields.chooseConnectionAddressStep?.subTitle.value}
            </label>
          </VisuallyHidden>
          {addressOptionsIsLoading ? (
            <Stack.Item>
              <RoundedSkeleton width={'100%'} height={'9rem'} />
              <RoundedSkeleton width={'100%'} height={'9rem'} />
            </Stack.Item>
          ) : (
            <Controller
              control={control}
              name={RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS}
              render={({ field: { onChange, value }, fieldState }) => (
                <RadioGroup
                  name={RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS}
                  aria-labelledby="RECTIFICATION_SCHEMA_FIELDS.SELECTED_ADDRESS"
                  onValueChange={onChange}
                  value={value}
                  error={fieldState.error?.message}>
                  <Stretch>
                    <Stack gap="3">
                      {rectificationAddressOptions?.addresses?.map(address => (
                        <RadioTile value={address.id!} alignLabel="start" key={address.id}>
                          {formattedAddress({
                            ...address.details,
                            busLabel:
                              fields.chooseConnectionAddressStep?.busLabel?.value ?? '[todo sitecore]: buslabel',
                          })}
                        </RadioTile>
                      ))}
                    </Stack>
                  </Stretch>
                </RadioGroup>
              )}
            />
          )}
        </Stack.Item>

        {error && <NotificationBox {...error} />}
        <Stack direction={'row'} gap={'3'}>
          <Button action="primary" size="regular" tone="onLight" type="button" onClick={onSubmit}>
            {fields.navigation.nextButtonLabel?.value}
          </Button>
          <Button action="secondary" size="regular" tone="onLight" type="button" onClick={() => onPreviousStep()}>
            {fields.navigation.previousButtonLabel?.value}
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};

export default SelectDeliveryAddressStep;
