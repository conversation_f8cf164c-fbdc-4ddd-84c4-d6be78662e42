import { useMemo, useState } from 'react';

import { subMonths, startOfMonth, endOfMonth, addMonths, addYears, formatISO } from 'date-fns';
import { StringParam, useQueryParam } from 'use-query-params';

import { TimeFrameDto } from '@dc-be/client';
import { useFormatter } from '@i18n';

export interface TimeFrame {
  timeFrame: TimeFrameDto;
  startDate: string;
  endDate: string;
}

export const getFirstAndLastDayOfLastSixYears = (currentDate?: Date): { startDate: string; endDate: string } => {
  const date = currentDate ?? new Date();
  const currentYear = date.getFullYear();
  return {
    startDate: `${currentYear - 5}-01-01`,
    endDate: `${currentYear}-12-31`,
  };
};

export const getFirstAndLastDayOfLastSixMonths = (currentDate?: Date): { startDate: string; endDate: string } => {
  const date = currentDate ?? new Date();
  const startDate = startOfMonth(subMonths(date, 6));
  const endDate = endOfMonth(date);

  return {
    startDate: formatISO(startDate, { representation: 'date' }),
    endDate: formatISO(endDate, { representation: 'date' }),
  };
};

export const useConsumptionGraphTimeFrame = () => {
  const [timeFrame, setTimeFrame] = useQueryParam('timeFrame', StringParam);
  const defaultStartDate = useMemo(() => {
    return timeFrame === 'Monthly'
      ? getFirstAndLastDayOfLastSixMonths().startDate
      : getFirstAndLastDayOfLastSixYears().startDate;
  }, [timeFrame]);
  const defaultEndDate = useMemo(() => {
    return timeFrame === 'Monthly'
      ? getFirstAndLastDayOfLastSixMonths().endDate
      : getFirstAndLastDayOfLastSixYears().endDate;
  }, [timeFrame]);
  const [startDate, setStartDate] = useState<string>(defaultStartDate);
  const [endDate, setEndDate] = useState<string>(defaultEndDate);

  const { date } = useFormatter();

  const handleTimeFrameChange = (timeFrame: TimeFrameDto) => {
    switch (timeFrame) {
      case 'Monthly':
        setTimeFrame(timeFrame);
        setStartDate(getFirstAndLastDayOfLastSixMonths().startDate);
        setEndDate(getFirstAndLastDayOfLastSixMonths().endDate);
        break;
      case 'Yearly':
        setTimeFrame(timeFrame);
        setStartDate(getFirstAndLastDayOfLastSixYears().startDate);
        setEndDate(getFirstAndLastDayOfLastSixYears().endDate);
        break;
    }
  };

  const changePeriod = (direction: 'previous' | 'next') => {
    const getBaseDate = (): Date => {
      if (direction === 'previous') {
        return new Date(startDate);
      }

      // For 'next' direction
      return timeFrame === 'Monthly' ? addMonths(new Date(endDate), 6) : addYears(new Date(endDate), 5);
    };

    const baseDate = getBaseDate();
    const newTimeFrameData =
      timeFrame === 'Monthly'
        ? getFirstAndLastDayOfLastSixMonths(baseDate)
        : getFirstAndLastDayOfLastSixYears(baseDate);

    setTimeFrame(timeFrame);
    setStartDate(newTimeFrameData.startDate);
    setEndDate(newTimeFrameData.endDate);
  };

  const getPeriodTitle = (timeFrame: string, startDate: string, endDate: string) => {
    switch (timeFrame) {
      case 'Monthly': {
        const yearFirstMonth = date.year(startDate) !== date.year(endDate) ? ` ${date.year(startDate)}` : '';
        return `${date.monthLong(startDate)}${yearFirstMonth} - ${date.monthLong(endDate)} ${date.year(endDate)}`;
      }
      case 'Yearly':
        return `${date.year(startDate)} - ${date.year(endDate)}`;
    }
  };

  return { timeFrame, setTimeFrame: handleTimeFrameChange, getPeriodTitle, changePeriod, startDate, endDate };
};
