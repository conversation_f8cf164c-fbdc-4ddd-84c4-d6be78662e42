import { useEffect } from 'react';

import { StringParam, useQueryParam } from 'use-query-params';

import { $ConsumptionTypeDto, ConsumptionTypeDto } from '@dc-be/client';

export const useConsumptionGraphConsumptionType = () => {
  const [consumptionType, setConsumptionType] = useQueryParam('consumptionType', StringParam);

  const checkConsumptionType = (consumptionType: string | null | undefined): ConsumptionTypeDto => {
    if (!consumptionType) {
      return 'Total';
    }
    return $ConsumptionTypeDto.enum.includes(consumptionType as ConsumptionTypeDto)
      ? (consumptionType as ConsumptionTypeDto)
      : 'Total';
  };

  useEffect(() => {
    setConsumptionType(checkConsumptionType(consumptionType));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [consumptionType]);

  return { consumptionType: checkConsumptionType(consumptionType), checkConsumptionType, setConsumptionType };
};
