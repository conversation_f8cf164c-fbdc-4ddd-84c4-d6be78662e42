import { FC } from 'react';

import { StringParam, useQueryParam } from 'use-query-params';

import { ConsumptionResponse } from '@dc-be/client';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { Box, Card, Divider, Expandable, Heading, Stack, Text } from '@sparky';

import { electricity, gas, injection } from './utils/colorMappings';

const LegendIcon = ({ color }: { color: string }) => {
  // eslint-disable-next-line dxp-rules/no-inline-css
  return <div style={{ width: 16, height: 16, borderRadius: '100%', backgroundColor: color }} />;
};

const ConsumptionGraphicalOverviewLegend: FC<{ consumptionData: ConsumptionResponse | null }> = ({
  consumptionData,
}) => {
  const [selected] = useQueryParam('selected', StringParam);
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();
  const { date: dateFormatter, unit } = useFormatter();

  const selectedPeriod = consumptionData?.periods?.find(period => period.startDate?.split('T')[0] === selected);

  if (!selected) {
    return null;
  }

  const periodMapping = new Map<string, { label: string; color: string; value: string }>();

  const electricityConsumption =
    selectedPeriod?.data?.find(data => data.energyType === 'Electricity' && data.direction === 'Consumption')?.value ??
    0;
  const gasConsumption = selectedPeriod?.data?.find(data => data.energyType === 'Gas')?.value ?? 0;
  const injectionConsumption =
    selectedPeriod?.data?.find(data => data.energyType === 'Electricity' && data.direction === 'Injection')?.value ?? 0;

  if (electricityConsumption > 0) {
    periodMapping.set('electricity', {
      label: fields.data.legendElectricityLabel?.value ?? 'Stroom',
      color: electricity,
      value: unit.electricity(electricityConsumption),
    });
  }
  if (gasConsumption > 0) {
    periodMapping.set('gas', {
      label: fields.data.legendGasLabel?.value ?? 'Gas',
      color: gas,
      value: unit.gas(gasConsumption),
    });
  }
  if (injectionConsumption > 0) {
    periodMapping.set('injection', {
      label: fields.data.legendInjectionLabel?.value ?? 'Opwek',
      color: injection,
      value: unit.electricity(injectionConsumption),
    });
  }

  return (
    <Card>
      <Box paddingY="4" paddingX="6">
        <Expandable defaultOpen>
          <Stack gap="3">
            <Expandable.Trigger label={'Text'}>
              <Stack>
                <Heading as="h3" size={'3XS'}>
                  {/* eslint-disable-next-line dxp-rules/no-inline-css */}
                  <span style={{ textTransform: 'capitalize' }}>
                    {dateFormatter.monthLong(selected)} {dateFormatter.year(selected)}
                  </span>
                </Heading>
                <Text weight={'bold'} color={'textLowEmphasis'}>
                  {fields.data.legendTitle?.value ?? 'Totaalverbruik'}
                </Text>
              </Stack>
            </Expandable.Trigger>
            <Expandable.Content>
              <Stack gap="3">
                <Text weight={'bold'} size={'BodyS'}>
                  {fields.data.legendTotalLabel?.value ?? 'Totaal'}
                </Text>
                <Divider />
                {Array.from(periodMapping.values()).map(period => (
                  <Stack direction={'row'} gap="3" alignY={'center'}>
                    <LegendIcon color={period.color} />
                    <Text size={'BodyS'} weight={'bold'}>
                      {period.label}
                    </Text>
                    <Text size={'BodyS'} weight={'bold'} color={'textLowEmphasis'}>
                      ({period.value})
                    </Text>
                  </Stack>
                ))}
              </Stack>
            </Expandable.Content>
          </Stack>
        </Expandable>
      </Box>
    </Card>
  );
};

export default ConsumptionGraphicalOverviewLegend;
