import { ConsumptionResponse, ConsumptionTypeDto, PeakResponse } from '@dc-be/client';

import { isConsumptionResponse, isPeakResponse } from './typeGuards';

/**
 * Filters and transforms consumption data based on the consumption type
 */
export const filterConsumptionData = (
  consumptionData: ConsumptionResponse | PeakResponse | null,
  consumptionType: ConsumptionTypeDto,
) => {
  if (!consumptionData) return [];

  if (consumptionType === 'PeakValues') {
    if (!isPeakResponse(consumptionData)) return [];
    return (
      consumptionData.periods?.map(p => {
        return {
          date: p.date,
          value: p.value,
        };
      }) || []
    );
  }

  if (!isConsumptionResponse(consumptionData)) return [];

  if (consumptionType === 'Total') {
    return (
      consumptionData.periods?.map(p => {
        // The order of the keys is important for the color scale
        return {
          date: p.startDate,
          gas: p.data?.find(d => d.energyType === 'Gas')?.value ?? 0,
          electricity: p.data?.find(d => d.energyType === 'Electricity' && d.direction === 'Consumption')?.value ?? 0,
          injection:
            -1 * (p.data?.find(d => d.energyType === 'Electricity' && d.direction === 'Injection')?.value ?? 0),
        };
      }) || []
    );
  }

  if (consumptionType === 'Injection') {
    return (
      consumptionData.periods?.map(p => {
        return {
          date: p.startDate,
          // we show the injection as a positive value here, so no need to invert it
          injection: p.data?.find(d => d.energyType === 'Electricity' && d.direction === 'Injection')?.value ?? 0,
        };
      }) || []
    );
  }

  if (consumptionType === 'Electricity') {
    return (
      consumptionData.periods?.map(p => {
        return {
          date: p.startDate,
          electricity: p.data?.find(d => d.energyType === 'Electricity' && d.direction === 'Consumption')?.value ?? 0,
          injection:
            -1 * (p.data?.find(d => d.energyType === 'Electricity' && d.direction === 'Injection')?.value ?? 0),
        };
      }) || []
    );
  }

  return (
    consumptionData.periods?.map(p => {
      return {
        date: p.startDate,
        [consumptionType?.toLowerCase()]: p.data?.find(d => d.energyType === consumptionType)?.value ?? 0,
      };
    }) || []
  );
};
