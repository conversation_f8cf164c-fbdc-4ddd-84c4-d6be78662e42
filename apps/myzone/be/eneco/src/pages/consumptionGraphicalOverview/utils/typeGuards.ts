import { ConsumptionResponse, PeakResponse } from '@dc-be/client';

export const isConsumptionResponse = (data: ConsumptionResponse | PeakResponse | null): data is ConsumptionResponse => {
  return (
    data !== null &&
    'periods' in data &&
    Array.isArray(data.periods) &&
    data.periods.length > 0 &&
    'data' in data.periods[0]
  );
};

export const isPeakResponse = (data: ConsumptionResponse | PeakResponse | null): data is PeakResponse => {
  return (
    data !== null &&
    'periods' in data &&
    Array.isArray(data.periods) &&
    data.periods.length > 0 &&
    'value' in data.periods[0]
  );
};
