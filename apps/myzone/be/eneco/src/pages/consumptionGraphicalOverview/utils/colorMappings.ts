import { ConsumptionTypeDto } from '@dc-be/client';
import { getTokens } from '@sparky/stitches';

const { colors } = getTokens();

export const electricity = colors.graphsElectricityPrimary.toString();
export const gas = colors.graphsGasPrimary.toString();
export const peakValues = colors.iconOnBackgroundVarTwo.toString();
export const injection = colors.graphsSolarPrimary.toString();

/**
 * Returns the appropriate color range for a given consumption type
 */
export const getColorRange = (consumptionType: ConsumptionTypeDto) => {
  const colorMap = {
    Total: [gas, electricity, injection],
    PeakValues: [peakValues],
    Injection: [injection],
    Gas: [gas],
    Electricity: [electricity, injection],
  };

  return colorMap[consumptionType] || [gas, electricity, injection];
};
