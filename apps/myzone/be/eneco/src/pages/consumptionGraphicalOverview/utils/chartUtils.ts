import { ConsumptionTypeDto } from '@dc-be/client';

export const getAdjustedHeight = ({
  consumptionType,
  height,
  key,
}: {
  consumptionType: ConsumptionTypeDto;
  height: number;
  key: string;
}) => {
  return consumptionType !== 'Injection' && key === 'injection' ? -height : height;
};

export const getAdjustedY = ({
  consumptionType,
  y,
  key,
  zeroLine,
}: {
  consumptionType: ConsumptionTypeDto;
  y: number;
  key: string;
  zeroLine: number;
}) => {
  return consumptionType !== 'Injection' && key === 'injection' ? zeroLine : y;
};
