import { FC, useEffect } from 'react';

import { ParentSize } from '@visx/responsive';
import { StringParam, useQueryParam } from 'use-query-params';

import { ConsumptionTypeDto, ConsumptionResponse, PeakResponse } from '@dc-be/client';
import { TimeFrameDto } from '@dc-be/client/types.gen';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { Bleed, Box, Card, Divider, Grid, Heading, IconButton, InputSelect, Stack, Stretch } from '@sparky';
import { ChevronLeftIcon, ChevronRightIcon } from '@sparky/icons';

import { ConsumptionGraphicalOverviewGraph } from './components/ConsumptionGraphicalOverviewGraph';
import MeterReadingsTable from './components/MeterReadingTable';
import PeakValuesConsumptionData from './components/PeakValuesConsumptionData';
import StandardConsumptionData from './components/StandardConsumptionData';
import ConsumptionGraphicalOverviewLegend from './ConsumptionGraphicalOverviewLegend';
import {
  getFirstAndLastDayOfLastSixMonths,
  getFirstAndLastDayOfLastSixYears,
  useConsumptionGraphTimeFrame,
} from './hooks/useConsumptionGraphTimeFrame';
import { AddressFilter } from '../../components/AddressFilter';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const ConsumptionGraphicalOverview: FC = () => {
  const { fields } = useContent<ConsumptionGraphicalOverviewRendering>();

  const { selectedAccount } = useSelfServiceAccount();
  const { addressIdentifier } = useAddressOptions();
  const { data: session } = useSession();

  const periodOptions = fields.data.periodOptionsList.value.enum;
  const consumptionTypeOptions = fields.data.consumptionTypeOptionsList.value.enum;

  const [consumptionType, setConsumptionType] = useQueryParam('consumptionType', StringParam);
  const { timeFrame, setTimeFrame, startDate, endDate, getPeriodTitle, changePeriod } = useConsumptionGraphTimeFrame();

  useEffect(() => {
    if (!consumptionType) {
      setConsumptionType('Total');
    }
  }, [consumptionType, setConsumptionType]);

  useEffect(() => {
    if (!timeFrame) {
      setTimeFrame('Monthly');
    }
  }, [timeFrame, setTimeFrame]);

  const periodTitle = getPeriodTitle(timeFrame ?? 'Monthly', startDate, endDate);

  const renderConsumptionContent = (consumptionData: ConsumptionResponse | PeakResponse | null, isLoading: boolean) => (
    <Bleed top="4">
      <Box paddingRight={{ initial: '1', md: '6' }} paddingBottom="8">
        <ParentSize>
          {({ width }) => (
            <ConsumptionGraphicalOverviewGraph
              isLoading={isLoading}
              timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
              consumptionType={(consumptionType as ConsumptionTypeDto) || 'Total'}
              consumptionData={consumptionData}
              width={width}
              height={500}
            />
          )}
        </ParentSize>
      </Box>
    </Bleed>
  );

  return (
    <Stretch>
      <Grid gridTemplateColumns="repeat(19, 1fr)" gap={6}>
        <Grid.Item gridColumn={'-1/1'}>
          <Box paddingTop="10">
            <Heading as="h1">{fields.data.title.value}</Heading>
          </Box>
        </Grid.Item>
        <Grid.Item gridColumn={'1/14'}>
          <Stack gap="6">
            <Stack direction="row" alignX="justify" wrap gap={4}>
              <Stack.Item>
                <AddressFilter label={fields.data.addressFilterLabel.value} />
              </Stack.Item>
              <Stack gap="2" direction="row">
                <InputSelect
                  label={fields.data.consumptionTypeLabel.value}
                  name="consumptionType"
                  options={consumptionTypeOptions}
                  placeholder=""
                  value={(consumptionType as ConsumptionTypeDto) || 'Total'}
                  onChange={e => setConsumptionType(e.currentTarget.value as ConsumptionTypeDto)}
                />
                <InputSelect
                  label={fields.data.periodLabel.value}
                  name="period"
                  options={periodOptions}
                  placeholder=""
                  value={timeFrame ?? 'Monthly'}
                  onChange={e => setTimeFrame(e.currentTarget.value as TimeFrameDto)}
                />
              </Stack>
            </Stack>
            <Card>
              <Stack>
                <Box padding="4">
                  <Stack alignX="justify" alignY="center" direction="row" gap="2">
                    <IconButton label="Previous period" onClick={() => changePeriod('previous')}>
                      <ChevronLeftIcon />
                    </IconButton>
                    <Heading as="h2" size="2XS">
                      {/* eslint-disable-next-line dxp-rules/no-inline-css */}
                      <span style={{ textTransform: 'capitalize' }}>{periodTitle}</span>
                    </Heading>
                    <IconButton
                      isDisabled={
                        endDate ===
                        (timeFrame === 'Monthly'
                          ? getFirstAndLastDayOfLastSixMonths().endDate
                          : getFirstAndLastDayOfLastSixYears().endDate)
                      }
                      label="Next period"
                      onClick={() => changePeriod('next')}>
                      <ChevronRightIcon />
                    </IconButton>
                  </Stack>
                </Box>
                <Divider />
                <Box paddingX="6" paddingTop="5">
                  <Stack direction="row" alignX="justify">
                    <Heading as="h3" size="3XS">
                      {fields.data.graphTitle.value}
                    </Heading>
                  </Stack>
                </Box>
                {consumptionType === 'PeakValues' ? (
                  <PeakValuesConsumptionData
                    selectedAccountNumber={selectedAccount.crmAccountNumber}
                    addressIdentifier={addressIdentifier}
                    timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                    startDate={startDate}
                    endDate={endDate}
                    session={session}>
                    {({ consumptionData, isLoading }) => renderConsumptionContent(consumptionData, isLoading)}
                  </PeakValuesConsumptionData>
                ) : (
                  <StandardConsumptionData
                    selectedAccountNumber={selectedAccount.crmAccountNumber}
                    addressIdentifier={addressIdentifier}
                    consumptionType={consumptionType as Exclude<ConsumptionTypeDto, 'PeakValues'>}
                    timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                    startDate={startDate}
                    endDate={endDate}
                    session={session}>
                    {({ consumptionData, isLoading }) => renderConsumptionContent(consumptionData, isLoading)}
                  </StandardConsumptionData>
                )}
              </Stack>
            </Card>
            {consumptionType !== 'Total' && consumptionType !== 'PeakValues' && (
              <StandardConsumptionData
                selectedAccountNumber={selectedAccount.crmAccountNumber}
                addressIdentifier={addressIdentifier}
                consumptionType={consumptionType as Exclude<ConsumptionTypeDto, 'PeakValues'>}
                timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
                startDate={startDate}
                endDate={endDate}
                session={session}>
                {({ consumptionData }) => (
                  <MeterReadingsTable
                    consumptionData={consumptionData}
                    consumptionType={(consumptionType as ConsumptionTypeDto) || 'Total'}
                    addressIdentifier={addressIdentifier}
                    selectedAccount={selectedAccount}
                    fields={fields.meterReadingsTable}
                  />
                )}
              </StandardConsumptionData>
            )}
          </Stack>
        </Grid.Item>
        <Grid.Item gridColumn={'14/19'}>
          {consumptionType !== 'PeakValues' && (
            <StandardConsumptionData
              selectedAccountNumber={selectedAccount.crmAccountNumber}
              addressIdentifier={addressIdentifier}
              consumptionType={consumptionType as Exclude<ConsumptionTypeDto, 'PeakValues'>}
              timeFrame={(timeFrame ?? 'Monthly') as TimeFrameDto}
              startDate={startDate}
              endDate={endDate}
              session={session}>
              {({ consumptionData }) => <ConsumptionGraphicalOverviewLegend consumptionData={consumptionData} />}
            </StandardConsumptionData>
          )}
        </Grid.Item>
      </Grid>
    </Stretch>
  );
};

export default ConsumptionGraphicalOverview;
