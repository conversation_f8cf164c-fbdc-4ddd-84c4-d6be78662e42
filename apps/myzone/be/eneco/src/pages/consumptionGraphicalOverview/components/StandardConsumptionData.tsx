import { FC, ReactNode } from 'react';

import { Session } from 'next-auth';

import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumption,
  ConsumptionResponse,
  ConsumptionTypeDto,
  DisplayModeDto,
  TimeFrameDto,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';

interface StandardConsumptionDataProps {
  selectedAccountNumber: string;
  addressIdentifier: string;
  consumptionType: Exclude<ConsumptionTypeDto, 'PeakValues'>;
  timeFrame: TimeFrameDto;
  startDate: string;
  endDate: string;
  session: Session | null;
  children: (data: { consumptionData: ConsumptionResponse | null; isLoading: boolean }) => ReactNode;
}

const StandardConsumptionData: FC<StandardConsumptionDataProps> = ({
  selectedAccountNumber,
  addressIdentifier,
  consumptionType,
  timeFrame,
  startDate,
  endDate,
  session,
  children,
}) => {
  const query = {
    consumptionType: consumptionType,
    displayMode: 'Consumption' as DisplayModeDto,
    timeFrame: timeFrame,
    startDate,
    endDate,
  };

  const { data, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByDeliveryAddressIdConsumption,
    {
      path: {
        accountNumber: selectedAccountNumber,
        deliveryAddressId: addressIdentifier,
      },
      query,
    },
    [
      `/accounts/${selectedAccountNumber}/delivery-addresses/${addressIdentifier}/consumption`,
      timeFrame,
      startDate,
      endDate,
      consumptionType,
    ],
    session,
  );

  const consumptionData = unwrapData(data);

  return <>{children({ consumptionData, isLoading })}</>;
};

export default StandardConsumptionData;
