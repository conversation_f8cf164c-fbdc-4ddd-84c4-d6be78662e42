import { <PERSON>Bottom, AxisRight } from '@visx/axis';
import { localPoint } from '@visx/event';
import { GridRows } from '@visx/grid';
import { Group } from '@visx/group';
import { scaleBand, scaleLinear, scaleOrdinal } from '@visx/scale';
import { BarRounded, BarStack } from '@visx/shape';
import { BarGroupBar, BarStack as BarStackType, SeriesPoint } from '@visx/shape/lib/types';
import { useTooltip, useTooltipInPortal, defaultStyles } from '@visx/tooltip';
import { StringParam, useQueryParam } from 'use-query-params';

import { ConsumptionResponse, ConsumptionTypeDto, PeakResponse, TimeFrameDto } from '@dc-be/client';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { ConsumptionGraphicalOverviewRendering as EnergyMonitorRendering } from '@sitecore/types/ConsumptionGraphicalOverview';
import { useMediaQuery } from '@sparky/hooks';
import { getTokens } from '@sparky/stitches';

import { getAdjustedHeight, getAdjustedY } from '../utils/chartUtils';
import { getColorRange } from '../utils/colorMappings';
import { getConsumptionTotals, getInjectionTotals } from '../utils/dataCalculations';
import { filterConsumptionData } from '../utils/dataTransformers';
import { isConsumptionResponse } from '../utils/typeGuards';

const { colors } = getTokens();

export type BarStackProps = {
  width: number;
  height: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  events?: boolean;
  consumptionData: ConsumptionResponse | PeakResponse | null;
  timeFrame: TimeFrameDto;
  consumptionType: ConsumptionTypeDto;
  isLoading: boolean;
};

const defaultMargin = { top: 40, right: 60, bottom: 40, left: 0 };
const tooltipStyles = {
  ...defaultStyles,
  minWidth: 60,
  backgroundColor: colors.backgroundDark.toString(),
  color: colors.textInverted.toString(),
  borderRadius: 4,
  padding: 8,
};

export function ConsumptionGraphicalOverviewGraph({
  consumptionData,
  width,
  height,
  margin = defaultMargin,
  timeFrame,
  consumptionType,
  isLoading,
}: BarStackProps) {
  const { fields } = useContent<EnergyMonitorRendering>();
  const { date: dateFormatter, unit } = useFormatter();
  const isDesktop = useMediaQuery('md');

  const data = filterConsumptionData(consumptionData, consumptionType);

  type Data = (typeof data)[number];
  type StackBarGroupBar = Omit<BarGroupBar<string>, 'value' | 'key'> & {
    bar: SeriesPoint<Data>;
    key: string;
  };
  type TooltipData = {
    bar: SeriesPoint<Data>;
    key: string;
    index: number;
    height: number;
    width: number;
    x: number;
    y: number;
    color: string;
  };

  const { tooltipOpen, tooltipLeft, tooltipTop, tooltipData, hideTooltip, showTooltip } = useTooltip<TooltipData>();
  const { containerRef, TooltipInPortal } = useTooltipInPortal({
    scroll: true,
  });
  const [selected, setSelected] = useQueryParam('selected', StringParam);

  const colorScaleDomain = data[0]
    ? (Object.keys(data[0]).filter(d => d !== 'date' && d !== 'total') as Lowercase<
        Exclude<ConsumptionTypeDto, 'Total'>
      >[])
    : [];

  const consumptionTotals = getConsumptionTotals(consumptionData);
  const injectionTotals = getInjectionTotals(consumptionData);
  const consumptionDomain =
    consumptionType === 'Injection'
      ? [0, Math.max(...injectionTotals)]
      : [-Math.max(...injectionTotals), Math.max(...consumptionTotals)];
  const colorRange = getColorRange(consumptionType ?? 'Total');

  if (width < 10) return null;

  const xMax = width - margin.right;
  const yMax = height - margin.top - margin.bottom;

  const formatDate = (date: string) => {
    try {
      if (timeFrame === 'Yearly') {
        return dateFormatter.year(date);
      }
      return dateFormatter.monthShort(date);
    } catch {
      return '';
    }
  };

  const getDate = (d: Data) => d.date;

  const dateScaleDomain = isLoading ? Array.from({ length: 6 }, () => '') : (data.map(getDate) as string[]);

  const dateScale = scaleBand<string>({
    domain: dateScaleDomain,
    padding: isDesktop ? 0.6 : 0.2,
  });
  const consumptionScale = scaleLinear<number>({
    domain: isLoading ? [0, 100] : consumptionDomain,
    nice: true,
  });
  const colorScale = scaleOrdinal<Lowercase<ConsumptionTypeDto>, string>({
    domain: colorScaleDomain,
    range: colorRange,
  });

  dateScale.rangeRound([0, xMax]);
  consumptionScale.range([yMax, 0]);

  const zeroLine = consumptionScale(0);
  const peakValuesAverage = isConsumptionResponse(consumptionData)
    ? (consumptionData.periods?.filter(
        p => p.data?.find(d => d.energyType === 'Electricity' && d.direction === 'Consumption')?.value ?? 0,
      ).length ?? 1)
    : 1;
  const peakValuesAverageLine = consumptionScale(peakValuesAverage);

  const getDefaultBarProps = (
    groupBar: StackBarGroupBar,
    barStacks: BarStackType<Data, Lowercase<ConsumptionTypeDto>>[],
  ) => {
    const stackOrder = barStacks
      .flatMap(({ bars }) => bars)
      .filter(({ index, height }) => index === groupBar.index && height)
      .sort((a, b) => {
        const aY = a.key === 'injection' ? zeroLine : a.y;
        const bY = b.key === 'injection' ? zeroLine : b.y;

        return aY > bY ? -1 : 1;
      });

    const hasRoundedBottomCorners = stackOrder[0].key === groupBar.key && groupBar.height < 0;
    const hasRoundedTopCorners = stackOrder.slice(-1)?.[0].key === groupBar.key && groupBar.bar[0] >= 0;

    const barDate = groupBar.bar.data.date?.split('T')[0];
    const isSelected = selected === barDate;

    return {
      ...groupBar,
      y: getAdjustedY({ consumptionType, y: groupBar.y, key: groupBar.key, zeroLine }),
      height: getAdjustedHeight({ consumptionType, height: groupBar.height, key: groupBar.key }),
      fill: groupBar.color,
      stroke: isSelected ? colors.backgroundDark.toString() : 'none',
      strokeWidth: isSelected ? 2 : 0,
      style: {
        cursor: 'pointer',
      },
      radius: isDesktop ? 16 : 8,
      topLeft: hasRoundedTopCorners,
      topRight: hasRoundedTopCorners,
      bottomLeft: hasRoundedBottomCorners,
      bottomRight: hasRoundedBottomCorners,
      onMouseMove: (event: React.MouseEvent<SVGPathElement>) => {
        const eventSvgCoords = localPoint(event);
        const left = groupBar.x + groupBar.width / 2;
        showTooltip({
          tooltipData: groupBar,
          tooltipTop: eventSvgCoords?.y,
          tooltipLeft: left,
        });
      },
      onMouseLeave: () => {
        hideTooltip();
      },
    };
  };

  return (
    <svg width={width} height={height} ref={containerRef}>
      <GridRows
        top={margin.top}
        left={margin.left}
        scale={consumptionScale}
        width={xMax}
        height={yMax}
        stroke={colors.borderDividerLowEmphasis.toString()}
      />
      <Group top={margin.top}>
        <BarStack<Data, Lowercase<ConsumptionTypeDto>>
          data={data}
          keys={colorScaleDomain}
          x={getDate}
          xScale={dateScale}
          yScale={consumptionScale}
          color={colorScale}>
          {barStacks =>
            barStacks.map(barStack => {
              return barStack.bars.map(bar => {
                const height = getAdjustedHeight({ consumptionType, height: bar.height, key: bar.key });
                if (height === 0 && bar.y === zeroLine) {
                  return (
                    <g key={`bar-stack-${barStack.index}-${bar.index}`}>
                      <BarRounded
                        height={16}
                        width={bar.width}
                        x={bar.x}
                        y={bar.y - 16}
                        radius={16}
                        top
                        fill={colors.graphsEstimatedPrimary.toString()}></BarRounded>
                    </g>
                  );
                }
                return (
                  <g key={`bar-stack-${barStack.index}-${bar.index}`}>
                    {height > 0 && (
                      <BarRounded
                        {...getDefaultBarProps(bar, barStacks)}
                        key={null}
                        onClick={() => {
                          const date = bar.bar.data.date?.split('T')[0];
                          if (date) {
                            setSelected(date);
                          }
                        }}></BarRounded>
                    )}
                  </g>
                );
              });
            })
          }
        </BarStack>
      </Group>
      <line
        x1={margin.left}
        x2={xMax}
        y1={isNaN(zeroLine) ? 0 : zeroLine + margin.top}
        y2={isNaN(zeroLine) ? 0 : zeroLine + margin.top}
        stroke={colors.borderDividerLowEmphasis.toString()}
        strokeWidth={2}
        strokeOpacity={0.5}
      />
      {consumptionType === 'PeakValues' && (
        <line
          x1={margin.left}
          x2={xMax}
          y1={peakValuesAverageLine + margin.top}
          y2={peakValuesAverageLine + margin.top}
          stroke={colors.feedbackInfo.toString()}
          strokeWidth={1}
          strokeDasharray="4 4"
        />
      )}
      <AxisBottom
        top={yMax + margin.top}
        scale={dateScale}
        tickFormat={formatDate}
        stroke={colors.borderDividerLowEmphasis.toString()}
        hideTicks
        tickLabelProps={{
          fill: colors.textPrimary.toString(),
          fontSize: 14,
          textAnchor: 'middle',
          fontFamily: 'Etelka',
          fontWeight: '400',
        }}
      />
      <AxisRight
        top={margin.top}
        left={xMax}
        scale={consumptionScale}
        tickFormat={consumptionType === 'PeakValues' ? value => `${value} kW` : value => `${value} kWh`}
        hideAxisLine
        hideTicks
        tickLabelProps={{
          fontSize: 12,
          textAnchor: 'end',
          fill: isLoading ? colors.backgroundPrimary.toString() : colors.textPrimary.toString(),
          fontFamily: 'Etelka',
          fontWeight: '400',
          width: 42,
          verticalAnchor: 'middle',
          transform: 'translate(40, 0)',
        }}
      />
      {tooltipOpen && tooltipData && (
        // eslint-disable-next-line dxp-rules/no-inline-css
        <TooltipInPortal
          style={tooltipStyles}
          top={tooltipTop}
          left={tooltipLeft}
          key={`tooltip-${tooltipData.key}-${tooltipData.index}`}>
          <div>
            <p>
              {
                fields.data.consumptionTypeOptionsList.value.enum.find(c => c.value.toLowerCase() === tooltipData.key)
                  ?.label
              }
            </p>
            <p>{unit.electricity(Number(tooltipData.bar.data[tooltipData.key as keyof Data]))}</p>
          </div>
        </TooltipInPortal>
      )}
    </svg>
  );
}
