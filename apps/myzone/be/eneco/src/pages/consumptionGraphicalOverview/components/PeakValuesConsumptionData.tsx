import { FC, ReactNode } from 'react';

import { Session } from 'next-auth';

import {
  DisplayModeDto,
  PeakResponse,
  TimeFrameDto,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptar,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';

interface PeakValuesConsumptionDataProps {
  selectedAccountNumber: string;
  addressIdentifier: string;
  timeFrame: TimeFrameDto;
  startDate: string;
  endDate: string;
  session: Session | null;
  children: (data: { consumptionData: PeakResponse | null; isLoading: boolean }) => ReactNode;
}

const PeakValuesConsumptionData: FC<PeakValuesConsumptionDataProps> = ({
  selectedAccountNumber,
  addressIdentifier,
  timeFrame,
  startDate,
  endDate,
  session,
  children,
}) => {
  const query = {
    consumptionType: 'PeakValues' as const,
    displayMode: 'Consumption' as DisplayModeDto,
    timeFrame: timeFrame,
    startDate,
    endDate,
  };

  const { data, isLoading } = useAuthenticatedDCBE(
    // TODO: this will be changed to another endpoint once the new endpoint is available
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierCaptar,
    {
      path: {
        accountNumber: selectedAccountNumber,
        addressIdentifier,
      },
      query,
    },
    [
      `/accounts/${selectedAccountNumber}/delivery-addresses/${addressIdentifier}/consumption`,
      timeFrame,
      startDate,
      endDate,
      'PeakValues',
    ],
    session,
  );

  const consumptionData = unwrapData(data);

  return <>{children({ consumptionData, isLoading })}</>;
};

export default PeakValuesConsumptionData;
