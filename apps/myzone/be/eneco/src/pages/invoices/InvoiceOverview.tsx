import { Fragment, useEffect, useState } from 'react';

import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoices,
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibility,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be';
import { useContent } from '@sitecore/common';
import { MyEnecoInvoiceRendering, NotificationField } from '@sitecore/types/MyEnecoInvoice';
import { Bleed, Card, Divider, Heading, InputSelect, NotificationBox, Stack } from '@sparky';
import { useTracking } from '@tracking';

import InvoiceExpandable from './InvoiceExpandable';
import InvoiceOpenAmountCard from './InvoiceOpenAmountCard';
import { getDateFilterOptions } from './utils/getDateFilterOptions';
import { AddressFilter } from '../../components/AddressFilter';
import DataErrorNotification from '../../components/DataErrorNotification';
import { LoadingListSpinner } from '../../components/Loading/LoadingListSpinner';
import { useAddressOptions } from '../../hooks/useAddressOptions';

const InvoiceOverview = () => {
  const { fields } = useContent<MyEnecoInvoiceRendering>();
  const { addressIdentifier } = useAddressOptions();

  const {
    selectedAccount: { crmAccountNumber: accountNumber },
    isProspect,
  } = useSelfServiceAccount();
  const { data: session } = useSession();

  const { data, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberContactDetails,
    {
      path: {
        accountNumber,
      },
    },
    [`/accounts/${accountNumber}/contact/details`],
    session,
  );

  const accountData = unwrapData(data);

  const dateFilterOptions = getDateFilterOptions({
    label: fields.invoiceDetails.pastTwelveMonthsLabel?.value,
    createdDate: accountData?.createdDate,
  });

  const [dateFilter, setDateFilter] = useState<string>(dateFilterOptions[0].value);

  const { data: unwrappedInvoiceDataWithoutDateParams } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoices,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
    },
    [`/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/invoices`],
    session,
  );

  const invoiceDataWithoutDateParams = unwrapData(unwrappedInvoiceDataWithoutDateParams);

  if (isProspect) return null;

  return (
    <Bleed top="8">
      <Stack gap="6">
        <InvoiceOpenAmountCard invoiceData={invoiceDataWithoutDateParams} fields={fields} />
        <Heading size="S" as="h2">
          {fields.filters.title.value}
        </Heading>
        <PaymentPlanNotification
          paymentPlanNotification={fields.filters.paymentPlanNotification}
          accountNumber={accountNumber}
        />
        <Stack direction={'row'} gap="3">
          <AddressFilter label={fields.filters.filterByAddressLabel.value} />
          <Stack.Item grow>
            {dateFilterOptions.length > 1 && (
              <InputSelect
                label={fields.filters.filterByTimeFrameLabel.value}
                options={dateFilterOptions}
                name="timeFrame"
                placeholder=""
                onChange={e => setDateFilter(e.currentTarget.value)}
              />
            )}
          </Stack.Item>
        </Stack>
        <DeliveryAddressInvoices
          isLoadingAddress={isLoading}
          emptyStateNotification={fields.filters.emptyStateNotification}
          paymentPlanNotification={fields.filters.paymentPlanNotification}
          addressIdentifier={addressIdentifier}
          dateFilter={dateFilter}
        />
      </Stack>
    </Bleed>
  );
};

export default InvoiceOverview;

interface DeliveryAddressProductsProps {
  addressIdentifier: string;
  dateFilter: string;
  emptyStateNotification: NotificationField;
  isLoadingAddress: boolean;
  paymentPlanNotification: NotificationField;
}

const DeliveryAddressInvoices = ({
  addressIdentifier,
  dateFilter,
  emptyStateNotification,
  paymentPlanNotification,
  isLoadingAddress,
}: DeliveryAddressProductsProps) => {
  const { data: session } = useSession();
  const { trackEventBE } = useTracking();
  const {
    selectedAccount: { crmAccountNumber: accountNumber },
  } = useSelfServiceAccount();

  const invoiceFromDate = dateFilter?.split('|')[0];
  const invoiceToDate = dateFilter?.split('|')[1];

  const {
    data: unwrappedInvoiceData,
    isLoading: isLoadingInvoice,
    error: errorInvoiceData,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberDeliveryAddressesByAddressIdentifierInvoices,
    {
      path: {
        accountNumber,
        addressIdentifier,
      },
      query: {
        InvoiceFromDate: invoiceFromDate,
        InvoiceToDate: invoiceToDate,
      },
    },
    [`/accounts/${accountNumber}/delivery-addresses/${addressIdentifier}/invoices`, invoiceFromDate, invoiceToDate],
    session,
  );

  const {
    data: unwrappedEligibilityData,
    isLoading: isLoadingEligibility,
    error: errorEligibility,
  } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibility,
    {
      path: { accountNumber },
      query: {
        includeConditions: true,
      },
    },
    [`/accounts/${accountNumber}/payment-plans/eligibility`],
    session,
  );

  const invoiceData = unwrapData(unwrappedInvoiceData);
  const eligibilityData = unwrapData(unwrappedEligibilityData);
  const isLoading = isLoadingInvoice || isLoadingEligibility || isLoadingAddress;

  useEffect(() => {
    if (eligibilityData?.billingAccountNumbers || eligibilityData?.conditions)
      trackEventBE('be_billing_deferral_is_eligible', { data: { isEligible: eligibilityData?.isEligible } });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eligibilityData]);

  if (isLoading) {
    return <LoadingListSpinner />;
  }

  if (
    !isLoading &&
    ((errorInvoiceData !== undefined && unwrappedInvoiceData === undefined) ||
      (errorEligibility !== undefined && unwrappedEligibilityData === undefined))
  ) {
    return <DataErrorNotification></DataErrorNotification>;
  }

  return (
    <>
      {invoiceData?.invoices?.length === 0 && (
        <NotificationBox
          isAlert={false}
          title={emptyStateNotification?.value.title}
          text={<RichText html={emptyStateNotification?.value.content} />}
          variant={emptyStateNotification?.value.variant}
        />
      )}
      <Card>
        {invoiceData?.invoices?.map((invoice, index) => (
          <Fragment key={invoice.invoiceId}>
            <InvoiceExpandable {...invoice} isEligibleForPaymentPlan={eligibilityData?.isEligible ?? false} />
            {(invoiceData.invoices ?? []).length > index + 1 && <Divider />}
          </Fragment>
        ))}
      </Card>
    </>
  );
};

interface PaymentPlanNotificationProps {
  paymentPlanNotification: NotificationField;
  accountNumber: string;
}

const PaymentPlanNotification = ({ paymentPlanNotification, accountNumber }: PaymentPlanNotificationProps) => {
  const { data: session } = useSession();
  const { data: unwrappedEligibilityData, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberPaymentPlansEligibility,
    {
      path: { accountNumber },
      query: {
        includeConditions: true,
      },
    },
    [`/accounts/${accountNumber}/payment-plans/eligibility`],
    session,
  );

  const eligibilityData = unwrapData(unwrappedEligibilityData);

  if (isLoading || !eligibilityData?.conditions?.hasExistingPaymentPlan) {
    return null;
  }

  return (
    <NotificationBox
      isAlert={false}
      title={paymentPlanNotification?.value.title}
      text={<RichText html={paymentPlanNotification?.value.content} />}
      variant={paymentPlanNotification?.value.variant}
    />
  );
};
