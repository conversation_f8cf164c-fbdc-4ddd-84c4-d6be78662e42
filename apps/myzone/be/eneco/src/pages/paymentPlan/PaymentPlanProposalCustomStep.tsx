import { useEffect, useState } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';

import RichText from '@components/RichText/RichText';
import {
  getEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulation,
  postEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlans,
} from '@dc-be/client';
import useAuthenticatedDCBE from '@dc-be/hooks/useAuthenticatedDCBE';
import unwrapData from '@dc-be/utils/unwrapData';
import { useSession } from '@dxp-auth';
import { useSelfServiceAccount } from '@dxp-auth-be/useSelfServiceAccount';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { PaymentPlanProposalStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Button, ButtonLink, Checkbox, Form, Heading, InputStepper, Stack, Table, Text } from '@sparky';
import { useTracking } from '@tracking';

import { BILLING_ACCOUNT_NUMBER_PARAM, PAYMENT_PLAN_PREVIOUS_STEP_PARAM } from './utils/constants';
import { CustomFormSchema, customFormSchema } from './utils/schemas';
import { LoadingText } from '../../components/Loading/LoadingText';
import { useRedirectAndNotifyBE } from '../../hooks/useRedirectAndNotifyBE';
import { useSetNotificationAndScrollToTop } from '../../hooks/useSetNotificationAndScrollToTop';
import { flowStepNameToLink } from '../../utils/flow/flowStepNameToLink';
import { inlineStrongReplacement } from '../../utils/richTextReplacements';

export const MIN_AMOUNT_OF_SLICES = 2;

export const PaymentPlanProposalCustomStep = () => {
  const { fields } = useContent<PaymentPlanProposalStepRenderingExtended>();
  const { trackEventBE } = useTracking();
  const { format, number, date, currency } = useFormatter();

  const { selectedAccount } = useSelfServiceAccount();
  const { data: session } = useSession();
  const { query } = useRouter();
  const { mutate } = useSWRConfig();
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const redirectAndNotify = useRedirectAndNotifyBE();
  const setNotification = useSetNotificationAndScrollToTop();

  const billingAccountNumber = query[BILLING_ACCOUNT_NUMBER_PARAM] as string;
  const previousStep = query[PAYMENT_PLAN_PREVIOUS_STEP_PARAM] as string;

  const nextStepUrl = flowStepNameToLink('Onboarding', fields.navigation.flowNavigationLinkList);
  const previousStepUrl = `${flowStepNameToLink('Type', fields.navigation.flowNavigationLinkList)}?${BILLING_ACCOUNT_NUMBER_PARAM}=${billingAccountNumber}&ps=${previousStep}`;

  const form = useForm<CustomFormSchema>({
    resolver: yupResolver(customFormSchema(fields)),
    defaultValues: {
      amountOfSlices: MIN_AMOUNT_OF_SLICES,
    },
  });

  const {
    control,
    formState: { errors },
    watch,
    handleSubmit,
    register,
  } = form;

  const amountOfSlices = watch('amountOfSlices');

  const { data, isLoading } = useAuthenticatedDCBE(
    getEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlansSimulation,
    {
      path: {
        accountNumber: selectedAccount.crmAccountNumber,
        billingAccountNumber,
      },
      query: {
        amountOfSlices,
      },
    },
    [
      `/accounts/${selectedAccount.crmAccountNumber}/billing-accounts/${billingAccountNumber}/payment-plans/simulation`,
      amountOfSlices.toString(),
    ],
    session,
  );

  const { openAmount, slices, maxAmountOfSlices, end } = unwrapData(data) ?? {};

  useEffect(() => {
    if (openAmount) {
      setTotalAmount(openAmount);
    }
  }, [openAmount]);

  const onSubmit = async (data: CustomFormSchema) => {
    try {
      const { response } =
        await postEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlans({
          path: {
            accountNumber: selectedAccount.crmAccountNumber,
            billingAccountNumber,
          },
          body: {
            amountOfSlices: data.amountOfSlices,
          },
        });

      if (response.ok) {
        trackEventBE('be_billing_pla,_confirm', { status: 'success', data: { amountOfSlices } });
        mutate(`/accounts/${selectedAccount.crmAccountNumber}/payment-plans`);
        redirectAndNotify({
          route: nextStepUrl,
          title: fields.termsAndConditions.splitPaymentSuccessNotification.value.title,
          text: (
            <RichText
              html={format(fields.termsAndConditions.splitPaymentSuccessNotification.value.content, {
                amount: currency.euro(totalAmount),
                date: date.medium(end ?? ''),
              })}
            />
          ),
          variant: fields.termsAndConditions.splitPaymentSuccessNotification.value.variant,
        });
        return;
      }

      if (!response.ok) throw new Error('API request failed');
    } catch (e) {
      void e;
      trackEventBE('be_billing_pla,_confirm', { status: 'failed' });
      return setNotification({
        notificationOptions: {
          title: fields.termsAndConditions.generalErrorNotification.value.title,
          text: <RichText html={fields.termsAndConditions.generalErrorNotification.value.content} />,
          variant: fields.termsAndConditions.generalErrorNotification.value.variant,
        },
        shouldScroll: true,
      });
    }
  };
  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <Stack gap="6" alignX="start">
        <Heading as="h1" size="M">
          {fields.proposalTableForm.title.value}
        </Heading>
        <div>
          <RichText
            html={format(fields.proposalTableForm.description.value, { amount: currency.euro(totalAmount) })}
            replacements={inlineStrongReplacement}
          />
        </div>
        <Table>
          <Table.Header>
            <span></span>
            <span>{fields.proposalTableForm.expirationDateLabel.value}</span>
            <span>{fields.proposalTableForm.amountIncludingVatLabel.value}</span>
            <span>{fields.proposalTableForm.remainingBalanceLabel.value}</span>
          </Table.Header>
          {isLoading
            ? Array.from({ length: Math.min(amountOfSlices, maxAmountOfSlices ?? 8) }).map((_, index) => (
                <Table.Row key={index}>
                  <Text whiteSpace="nowrap">
                    {fields.proposalTableForm.sliceLabel.value} {index + 1}
                  </Text>
                  <span>
                    <LoadingText width={50} />
                  </span>
                  <span>
                    <LoadingText width={50} />
                  </span>
                  <span>
                    <LoadingText width={50} />
                  </span>
                </Table.Row>
              ))
            : slices?.map((slice, index) => {
                const { start, end, amount } = slice;
                const remainingBalance =
                  totalAmount - slices?.slice(0, index + 1).reduce((acc, slice) => acc + (slice.amount ?? 0), 0);

                return (
                  <Table.Row key={start}>
                    <Text whiteSpace="nowrap">
                      {fields.proposalTableForm.sliceLabel.value} {index + 1}
                    </Text>
                    <span>{end}</span>
                    <span>€ {number.twoFractionDigits(amount ?? 0)}</span>
                    <span>€ {number.twoFractionDigits(Math.abs(remainingBalance))}</span>
                  </Table.Row>
                );
              })}
        </Table>
        <Controller
          control={control}
          name="amountOfSlices"
          render={({ field }) => (
            <InputStepper
              label={fields.proposalTableForm.adjustSliceNumberFormField.value.label}
              hint={
                <RichText
                  html={format(fields.proposalTableForm.adjustSliceNumberFormField.value.hint, {
                    max: maxAmountOfSlices ?? 8,
                  })}
                />
              }
              min={MIN_AMOUNT_OF_SLICES}
              defaultValue={MIN_AMOUNT_OF_SLICES}
              max={maxAmountOfSlices ?? 8}
              error={errors.amountOfSlices?.message?.toString()}
              {...field}
              onFocus={e => {
                e.currentTarget.select();
              }}
              onChange={e => {
                // This is a workaround for the input stepper to not be able to type a number that's too large
                const value = Number(e.target.value);
                e.target.blur();

                if (isNaN(value)) {
                  return field.onChange(MIN_AMOUNT_OF_SLICES);
                }

                if (value < MIN_AMOUNT_OF_SLICES) {
                  return field.onChange(MIN_AMOUNT_OF_SLICES);
                }

                if (value > (maxAmountOfSlices ?? 8)) {
                  return field.onChange(maxAmountOfSlices ?? 8);
                }

                return field.onChange(e);
              }}
            />
          )}
        />
        <Stack gap="2">
          <RichText html={fields.termsAndConditions.description.value} />
          <Checkbox
            label={fields.termsAndConditions.termsAndConditionsCheckboxFormField.value.label}
            hint={format(fields.termsAndConditions.termsAndConditionsCheckboxFormField.value.hint, {
              max: maxAmountOfSlices ?? 8,
            })}
            error={errors.termsAndConditions?.message?.toString()}
            {...register('termsAndConditions')}
          />
        </Stack>
        <Stack direction="row" gap="5">
          <Button type="submit">{fields.navigation.nextButtonText.value}</Button>
          <ButtonLink action="secondary" href={previousStepUrl}>
            {fields.navigation.previousButtonText.value}
          </ButtonLink>
        </Stack>
      </Stack>
    </Form>
  );
};
