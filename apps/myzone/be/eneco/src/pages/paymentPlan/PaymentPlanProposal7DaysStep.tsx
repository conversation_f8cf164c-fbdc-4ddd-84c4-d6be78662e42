import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { useSWRConfig } from 'swr';

import RichText from '@components/RichText/RichText';
import { postEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlans } from '@dc-be/client';
import { useSelfServiceAccount } from '@dxp-auth-be/useSelfServiceAccount';
import { useRouter } from '@dxp-next';
import { useFormatter } from '@i18n';
import { useContent } from '@sitecore/common';
import { PaymentPlanProposalStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Button, ButtonLink, Checkbox, Form, Heading, Stack } from '@sparky';
import { useTracking } from '@tracking';

import { useInvoices } from './hooks/useInvoices';
import { BILLING_ACCOUNT_NUMBER_PARAM, PAYMENT_PLAN_PREVIOUS_STEP_PARAM } from './utils/constants';
import { SevenDaysFormSchema, sevenDaysFormSchema } from './utils/schemas';
import { useRedirectAndNotifyBE } from '../../hooks/useRedirectAndNotifyBE';
import { useSetNotificationAndScrollToTop } from '../../hooks/useSetNotificationAndScrollToTop';
import { flowStepNameToLink } from '../../utils/flow/flowStepNameToLink';
import { inlineStrongReplacement } from '../../utils/richTextReplacements';

export const PaymentPlanProposal7DaysStep = () => {
  const { fields } = useContent<PaymentPlanProposalStepRenderingExtended>();

  const { format, date, currency } = useFormatter();
  const { totalAmount, calculated7daysDueDate } = useInvoices();
  const { query } = useRouter();
  const { selectedAccount } = useSelfServiceAccount();
  const { trackEventBE } = useTracking();

  const billingAccountNumber = query[BILLING_ACCOUNT_NUMBER_PARAM] as string;
  const previousStep = query[PAYMENT_PLAN_PREVIOUS_STEP_PARAM] as string;
  const formSchema = sevenDaysFormSchema(fields);

  const { mutate } = useSWRConfig();
  const redirectAndNotify = useRedirectAndNotifyBE();
  const setNotification = useSetNotificationAndScrollToTop();

  const nextStepUrl = flowStepNameToLink('Onboarding', fields.navigation.flowNavigationLinkList);
  const previousStepUrl =
    flowStepNameToLink('Type', fields.navigation.flowNavigationLinkList) +
    `?${BILLING_ACCOUNT_NUMBER_PARAM}=${billingAccountNumber}&ps=${previousStep}`;

  const onSubmit = async () => {
    try {
      const { response } =
        await postEnecoBeXapiSiteApiV1AccountsByAccountNumberBillingAccountsByBillingAccountNumberPaymentPlans({
          path: {
            accountNumber: selectedAccount.crmAccountNumber,
            billingAccountNumber,
          },
          body: {
            amountOfSlices: 1,
          },
        });

      if (response.ok) {
        trackEventBE('be_billing_deferral_confirm', { status: 'success' });
        mutate(`/accounts/${selectedAccount.crmAccountNumber}/payment-plans`);
        redirectAndNotify({
          route: nextStepUrl,
          title: fields.termsAndConditions.deferralSuccessNotification.value.title,
          text: (
            <RichText
              html={format(fields.termsAndConditions.deferralSuccessNotification.value.content, {
                amount: currency.euro(totalAmount ?? 0),
                date: date.medium(calculated7daysDueDate),
              })}
            />
          ),
          variant: fields.termsAndConditions.deferralSuccessNotification.value.variant,
        });
        return;
      }

      if (!response.ok) throw new Error('Something went wrong');
    } catch (e) {
      void e;
      trackEventBE('be_billing_deferral_confirm', { status: 'failed', message: 'unexpected error occured' });
      return setNotification({
        notificationOptions: {
          title: fields.termsAndConditions.generalErrorNotification.value.title,
          text: <RichText html={fields.termsAndConditions.generalErrorNotification.value.content} />,
          variant: fields.termsAndConditions.generalErrorNotification.value.variant,
        },
        shouldScroll: true,
      });
    }
  };

  const form = useForm<SevenDaysFormSchema>({
    resolver: yupResolver(formSchema),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = form;

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <Stack gap="6" alignX="start">
        <Heading as="h1" size="M">
          {fields.paymentArrangement.title.value}
        </Heading>
        <div>
          <RichText
            html={format(fields.paymentArrangement.description.value, {
              amount: currency.euro(totalAmount ?? 0),
              date: date.medium(calculated7daysDueDate),
            })}
            replacements={inlineStrongReplacement}
          />
        </div>
        <Stack gap="2">
          <RichText html={fields.termsAndConditions.description.value} />
          <Checkbox
            label={fields.termsAndConditions.termsAndConditionsCheckboxFormField.value.label}
            hint={fields.termsAndConditions.termsAndConditionsCheckboxFormField.value.hint}
            error={errors.termsAndConditions?.message?.toString()}
            {...register('termsAndConditions')}
          />
        </Stack>
        <Stack direction="row" gap="5">
          <Button type="submit">{fields.navigation.nextButtonText.value}</Button>
          <ButtonLink action="secondary" href={previousStepUrl}>
            {fields.navigation.previousButtonText.value}
          </ButtonLink>
        </Stack>
      </Stack>
    </Form>
  );
};
