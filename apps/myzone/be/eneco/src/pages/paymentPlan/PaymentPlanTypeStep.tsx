import { FC, useEffect } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import RichText from '@components/RichText/RichText';
import { useSelfServiceAccount } from '@dxp-auth-be/useSelfServiceAccount';
import { useRouter } from '@dxp-next';
import { useContent } from '@sitecore/common';
import { PaymentPlanTypeStepRenderingExtended } from '@sitecore/types/manual/ItemWithNavigationExtended';
import { Box, Button, ButtonLink, Form, Heading, RadioGroup, RadioTile, Stack } from '@sparky';
import { useTracking } from '@tracking';

import useOnboardingUrl from './hooks/useOnboardingUrl';
import { BILLING_ACCOUNT_NUMBER_PARAM, PAYMENT_PLAN_PREVIOUS_STEP_PARAM } from './utils/constants';
import { flowStepNameToLink } from '../../utils/flow/flowStepNameToLink';

export const paymentPlanTypes = ['7days', 'custom'] as const;
export type PaymentPlanType = (typeof paymentPlanTypes)[number];

const PaymentPlanTypeStep: FC = () => {
  const { fields } = useContent<PaymentPlanTypeStepRenderingExtended>();
  const { query, push } = useRouter();
  const { isCurrentAccountReader } = useSelfServiceAccount();
  const { trackEventBE } = useTracking();

  const previousStep = query[PAYMENT_PLAN_PREVIOUS_STEP_PARAM] as string;
  const billingAccountNumber = query[BILLING_ACCOUNT_NUMBER_PARAM] as string;

  const FormSchema = yup.object({
    paymentPlanType: yup.string().required(),
  });

  const form = useForm<yup.InferType<typeof FormSchema>>({
    resolver: yupResolver(FormSchema),
  });

  const { control, handleSubmit } = form;

  const onboardingStepUrl = useOnboardingUrl({ flowNavigationLinkList: fields.navigation.flowNavigationLinkList });
  const previousStepUrl =
    flowStepNameToLink('Amount', fields.navigation.flowNavigationLinkList) +
    `?${BILLING_ACCOUNT_NUMBER_PARAM}=${billingAccountNumber}&ps=${previousStep}`;
  const nextStepUrl =
    flowStepNameToLink('Proposal', fields.navigation.flowNavigationLinkList) +
    `?${BILLING_ACCOUNT_NUMBER_PARAM}=${billingAccountNumber}&ps=${previousStep}`;

  useEffect(() => {
    if (isCurrentAccountReader) {
      push(onboardingStepUrl);
    }
  }, [isCurrentAccountReader, push, onboardingStepUrl]);

  const onSubmit = (data: yup.InferType<typeof FormSchema>) => {
    trackEventBE('be_billing_deferral_choice', { data });
    push(nextStepUrl + `&pt=${data.paymentPlanType}`);
  };

  return (
    <Box paddingTop="10">
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Stack gap="6" alignX="start">
          <Heading as="h1" size="M">
            {fields.form.title.value}
          </Heading>
          <RichText html={`<span id="paymentPlanType">${fields.form.description.value}</span>`} />
          <Controller
            control={control}
            name="paymentPlanType"
            render={({ field: { onChange, value } }) => (
              <RadioGroup
                aria-labelledby="paymentPlanType"
                direction="column"
                value={value}
                onValueChange={onChange}
                name="paymentPlanType">
                <RadioTile value="7days">{fields.form.sevenDaysExtentionOptionFormField.value.label}</RadioTile>
                <RadioTile value="custom">{fields.form.paymentPlanOptionFormField.value.label}</RadioTile>
              </RadioGroup>
            )}
          />
          <Stack gap="5" direction={'row'}>
            <Button type="submit">{fields.navigation.nextButtonText.value}</Button>
            <ButtonLink action="secondary" href={previousStepUrl}>
              {fields.navigation.previousButtonText.value}
            </ButtonLink>
          </Stack>
        </Stack>
      </Form>
    </Box>
  );
};

export default PaymentPlanTypeStep;
