import { MoveRegisterDto, TimeframeTypeCodeDto } from '@dc-be/client';

export type MeterReadingFlags = {
  showSingleMeterReading: boolean;
  showDayMeterReading: boolean;
  showNightMeterReading: boolean;
  showExclNightMeterReading: boolean;
};

export const meterRegisterMapper = (registers: Registers[] | undefined): MeterReadingFlags => {
  let meterReadingFlags: MeterReadingFlags = {
    showSingleMeterReading: false,
    showDayMeterReading: false,
    showNightMeterReading: false,
    showExclNightMeterReading: false,
  };

  registers?.forEach(register => {
    const result = singleRegisterMapper({ ...register, timeframeTypeCode: register.timeframeTypeCode });
    meterReadingFlags = { ...meterReadingFlags, ...result };
  });

  return meterReadingFlags;
};

type Registers = Omit<MoveRegisterDto, 'timeframeTypeCode'> & {
  timeframeTypeCode: TimeframeTypeCodeDto | undefined;
};

export const singleRegisterMapper = (register: Registers): Partial<MeterReadingFlags> => {
  switch (register?.timeframeTypeCode) {
    case 'TH':
      return { showSingleMeterReading: true };
    case 'HI':
      return { showDayMeterReading: true };
    case 'LO':
      return { showNightMeterReading: true };
    case 'EX':
      return { showExclNightMeterReading: true };
    default:
      return {};
  }
};
