import { screen } from '@testing-library/react';

import renderApp from '@jest-tools/renderApp';
import failureMock from '@mocks/sitecore/apps/launchpad/productPurchaseResultFailure';
import successMock from '@mocks/sitecore/apps/launchpad/productPurchaseResultSuccess';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';

import ProductPurchaseResult from './ProductPurchaseResult';

const { fields: successFields } = successMock;
const { fields: failureFields } = failureMock;

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useSubscriptionsUpdateRegisteredProductStatus: jest.fn().mockReturnValue({
    send: jest.fn(),
  }),
}));

describe('ProductPurchaseResult', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render success result', () => {
    renderApp(ProductPurchaseResult, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/purchase-dongle-success',
    });

    expect(screen.getByText(successFields?.resultTitel?.value, { exact: true })).toBeInTheDocument();
  });

  it('should render failure result', () => {
    renderApp(ProductPurchaseResult, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/purchase-dongle-failure',
    });

    expect(screen.getByText(failureFields?.resultTitel?.value, { exact: true })).toBeInTheDocument();
  });
});
