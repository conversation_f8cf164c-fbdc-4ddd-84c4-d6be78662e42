import { screen } from '@testing-library/react';

import renderApp from '@jest-tools/renderApp';
import failureMock from '@mocks/sitecore/apps/launchpad/productPurchaseResultFailure';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';

import ProductPurchaseResult from './ProductPurchaseResult';

const { fields: failureFields } = failureMock;

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useSubscriptionsUpdateRegisteredProductStatus: jest.fn().mockReturnValue({
    send: jest.fn(),
  }),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    basePath: '',
    asPath: '',
    query: {
      item: '/launchpad/purchase-smartinsights-failure',
      donglePurchased: 'true',
    },
  })),
  NextRouter: {},
}));

describe('ProductPurchaseResult', () => {
  it('should render failure result', () => {
    renderApp(ProductPurchaseResult, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/purchase-dongle-failure',
    });

    expect(screen.getByText(failureFields?.resultTitel?.value, { exact: true })).toBeInTheDocument();
  });
});
