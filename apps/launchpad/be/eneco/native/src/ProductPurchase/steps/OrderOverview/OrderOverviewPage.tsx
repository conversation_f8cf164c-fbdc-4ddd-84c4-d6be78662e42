import { FC, useMemo } from 'react';

import RichText from '@components/RichText/RichText';
import { useContent } from '@sitecore/common';
import { OrderOverviewRendering } from '@sitecore/types/manual/launchpad/OrderOverview';
import { ProductPurchaseFlowRendering } from '@sitecore/types/manual/launchpad/ProductPurchaseFlow';
import { AlertDialog, Box, Button, Expandable, InputText, Stack, Stretch, Text, VisuallyHidden } from '@sparky';
import { InfoIcon } from '@sparky/icons';

import { AppliedDiscountCodes } from './AppliedDiscountCodes';
import useCouponCode from '../../../hooks/useCouponCode';
import OrderFlowBasket from '../../components/OrderFlowBasket';
import OrderFlowBottomNavigation from '../../components/OrderFlowBottomNavigation';
import OrderFlowHeader from '../../components/OrderFlowHeader';
import OrderFlowProgress from '../../components/OrderFlowProgress';
import { ProductPurchaseMachineContext } from '../../ProductPurchaseFlow';

export const OrderOverviewPage: FC = () => {
  const { fields } = useContent<ProductPurchaseFlowRendering>();
  const {
    applyPromoCode,
    appliedCoupon,
    canApplyPromoCodes,
    newCoupon,
    promoCodeInput,
    setPromoCodeInput,
    replaceDiscount,
    resetOverrideDiscountStates,
    isLoadingValidateOrder,
    isErrorValidateOrder,
  } = useCouponCode();

  const productPurchaseMachineContext = ProductPurchaseMachineContext.useSelector(snapshot => snapshot.context);
  const basketItems = Array.from(productPurchaseMachineContext.basket?.values() ?? []);

  const overviewFields = useMemo(
    () =>
      fields.items.find<OrderOverviewRendering>(
        (item): item is OrderOverviewRendering => item.templateName === 'OrderOverview',
      )?.fields.data,
    [fields],
  );

  if (!overviewFields) {
    return null;
  }

  return (
    <Stretch>
      <Stack>
        <Stack.Item grow>
          <OrderFlowHeader />
          <OrderFlowProgress stepOrder={1} />
          <OrderFlowBasket
            title={overviewFields.orderOverviewBasketTitle.value}
            callToActionText={overviewFields.orderOverviewBasketCallToActionText.value}
          />

          <Box>
            <Expandable>
              <Expandable.Trigger label={overviewFields.orderOverviewDiscountCodeFormField.value.label}>
                <Stack direction="row" gap="1">
                  <Text weight="bold">{overviewFields.orderOverviewDiscountCodeFormField.value.label}</Text>
                  <Text> {overviewFields.orderOverviewDiscountCodeIsMandatoryText.value}</Text>
                </Stack>
              </Expandable.Trigger>
              <Expandable.Content>
                <Box paddingTop="5">
                  <Stack gap="4">
                    <Stack gap="5">
                      <AppliedDiscountCodes
                        currentPromoCode={promoCodeInput}
                        isValidatingCurrentPromoCode={isLoadingValidateOrder}
                        basketItems={basketItems}
                      />
                      <Text size="BodyS" color="textLowEmphasis">
                        {overviewFields.orderOverviewDiscountsFormField.value.label}
                      </Text>
                    </Stack>
                    <Stack direction="row" gap="2" alignX="justify">
                      <Stack.Item grow>
                        <InputText
                          error={
                            isErrorValidateOrder || !canApplyPromoCodes
                              ? overviewFields.orderOverviewDiscountsFormField.value.validationMessage
                              : undefined
                          }
                          onChange={val => setPromoCodeInput(val.target.value)}
                          value={promoCodeInput}
                          label="" // todo: a11y concerns: (placeholder should not be used as label)
                          aria-label={overviewFields.orderOverviewDiscountCodeFormField.value.label}
                          name="discount-code"
                          placeholder={overviewFields.orderOverviewDiscountCodeFormField.value.placeholder}
                        />
                      </Stack.Item>
                      <Stack.Item>
                        <Box paddingTop="2">
                          {overviewFields.orderOverviewApplyDiscountCodeLabel.value && (
                            <Button
                              size="compact"
                              action="secondary"
                              onClick={applyPromoCode}
                              tone="onLight"
                              isLoading={isLoadingValidateOrder}>
                              {overviewFields.orderOverviewApplyDiscountCodeLabel.value}
                            </Button>
                          )}
                        </Box>
                      </Stack.Item>
                    </Stack>
                    {overviewFields.orderOverviewDiscountsInfoText && (
                      <Stack direction="row" gap="2">
                        <Stack.Item>
                          <InfoIcon />
                        </Stack.Item>
                        <Stack.Item>
                          <Text size="BodyS">{overviewFields.orderOverviewDiscountsInfoText.value}</Text>
                        </Stack.Item>
                      </Stack>
                    )}
                  </Stack>
                </Box>
              </Expandable.Content>
            </Expandable>
          </Box>
        </Stack.Item>

        <OrderFlowBottomNavigation
          proceedButtonLabel={fields.data.proceedButtonLabel.value}
          backButtonLabel={fields.data.backButtonLabel.value}
        />
      </Stack>
      {fields.data.overrideDiscountDialog.value.submitButtonText &&
        fields.data.overrideDiscountDialog.value.cancelButtonText &&
        appliedCoupon?.redeemingCouponCode &&
        newCoupon?.redeemingCouponCode && (
          <AlertDialog
            setOpen={() => {}}
            isOpen={!!newCoupon}
            title={fields.data.overrideDiscountDialog.value.title}
            confirmText={fields.data.overrideDiscountDialog.value.submitButtonText}
            denyText={fields.data.overrideDiscountDialog.value.cancelButtonText}
            onConfirm={() => replaceDiscount()}
            onDeny={() => resetOverrideDiscountStates()}
            description={<VisuallyHidden>{fields.data.overrideDiscountDialog.value.title}</VisuallyHidden>}>
            <Text>
              <RichText
                html={fields.data.overrideDiscountDialog.value.content.replace(
                  '{NEW_CODE}',
                  newCoupon.redeemingCouponCode,
                )}
              />
            </Text>
          </AlertDialog>
        )}
    </Stretch>
  );
};
