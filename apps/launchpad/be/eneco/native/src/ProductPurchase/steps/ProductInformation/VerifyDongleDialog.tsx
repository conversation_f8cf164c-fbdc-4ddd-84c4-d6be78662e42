import { Dispatch, FC, SetStateAction } from 'react';

import { useContent } from '@sitecore/common';
import { ProductPurchaseFlowRendering } from '@sitecore/types/manual/launchpad/ProductPurchaseFlow';
import { AlertDialog, Text, VisuallyHidden } from '@sparky';

type Props = {
  dongleStatus?: DongleRequired;
  handleConfirm: () => void;
  openAlert: boolean;
  setOpenAlert: Dispatch<SetStateAction<boolean>>;
};

export enum DongleRequired {
  USER_WITH_DONGLE = 'USER_WITH_DONGLE',
  USER_WITHOUT_DONGLE = 'USER_WITHOUT_DONGLE',
}

const VerifyDongleDialog: FC<Props> = ({ dongleStatus, handleConfirm, openAlert, setOpenAlert }) => {
  const { fields } = useContent<ProductPurchaseFlowRendering>();

  if (!dongleStatus) {
    return null;
  }

  let confirmText = fields?.data.dongleAlreadyPurchasedDialog?.value.submitButtonText;
  let denyText = fields?.data.dongleAlreadyPurchasedDialog?.value.cancelButtonText;
  let title = fields?.data.dongleAlreadyPurchasedDialog?.value.title;
  let content = fields?.data.dongleAlreadyPurchasedDialog?.value.content;

  if (dongleStatus === DongleRequired.USER_WITHOUT_DONGLE) {
    confirmText = fields?.data.dongleRequiredDialog?.value.submitButtonText;
    denyText = fields?.data.dongleRequiredDialog?.value.cancelButtonText;
    title = fields?.data.dongleRequiredDialog?.value.title;
    content = fields?.data.dongleRequiredDialog?.value.content;
  }

  if (!confirmText || !denyText) {
    return null;
  }

  return (
    <AlertDialog
      confirmText={confirmText}
      denyText={denyText}
      onConfirm={handleConfirm}
      onDeny={() => {}}
      title={title}
      width="regular"
      isOpen={openAlert}
      setOpen={setOpenAlert}
      description={<VisuallyHidden>{title}</VisuallyHidden>}>
      <Text size="BodyM">{content}</Text>
    </AlertDialog>
  );
};

export default VerifyDongleDialog;
