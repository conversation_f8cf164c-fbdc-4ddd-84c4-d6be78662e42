import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useSubscriptionsValidateOrder } from '@dc/hooks';
import { getCustomerProfileBe, getAvailableProducts, getCustomerInfo } from '@dc/services';
import renderApp from '@jest-tools/renderApp';
import getCustomerProfileBeResponse from '@mocks/dc/customers/profile/be/default';
import profileCouponCodesResponse from '@mocks/dc/customers/profile/be/userCouponCodes';
import getCustomerWithDongleProfileBeResponse from '@mocks/dc/customers/profile/be/userWithDongle';
import { getAvailableProductsResponse as productsCouponCodesResponse } from '@mocks/dc/subscriptions/getAvailableProducts/couponCodes';
import { getSmartInsightsProduct } from '@mocks/dc/subscriptions/getAvailableProducts/smartInsightsGetAvailableProducts';
import customerInfoCouponCodesResponse from '@mocks/dc/subscriptions/getCustomerInfo/couponCodes';
import getCustomerInfoResponse from '@mocks/dc/subscriptions/getCustomerInfo/default';
import insightsMock from '@mocks/sitecore/containers/be/eneco/insights/';

import ProductPurchaseFlow from './ProductPurchaseFlow';

const mockAvaibleProducts = getSmartInsightsProduct;
const mockCustomerInfo = getCustomerInfoResponse;
const mockCustomerProfile = getCustomerProfileBeResponse();
const mockCustomerProfileWithDongle = getCustomerWithDongleProfileBeResponse();

if (typeof global.structuredClone !== 'function') {
  global.structuredClone = obj => JSON.parse(JSON.stringify(obj));
}

if (!window.HTMLElement.prototype.scrollIntoView) {
  window.HTMLElement.prototype.scrollIntoView = function () {};
}

jest.mock('@common/application', () => ({
  ...jest.requireActual('@common/application'),
  useApplication: jest.fn().mockReturnValue({
    locale: 'nl-BE',
    locales: ['nl-BE'],
    language: 'nl',
    languages: ['nl'],
    searchParams: new URLSearchParams(),
    isEditMode: false,
  }),
}));

jest.mock('@dc/hooks', () => ({
  ...jest.requireActual('@dc/hooks'),
  useSubscriptionsValidateOrder: jest.fn(),
}));

jest.mock('@dc/services', () => ({
  ...jest.requireActual('@dc/services'),
  getAvailableProducts: jest.fn().mockImplementation(() => mockAvaibleProducts),
  getCustomerInfo: jest.fn().mockImplementation(() => mockCustomerInfo),
  getCustomerProfileBe: jest.fn().mockImplementation(() => mockCustomerProfile),
}));

describe('ProductPurchaseFlow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render and the user should see the primary product', async () => {
    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/smart-insights',
    });

    expect(await screen.findByText('EnergyMonitor Premium', { selector: 'h3', exact: true })).toBeInTheDocument();
  });

  test.skip('should be possible for the user to complete a purchase', async () => {
    const validateOrderMock = jest.fn().mockResolvedValue({ success: true });
    (useSubscriptionsValidateOrder as jest.Mock).mockReturnValue({
      send: validateOrderMock,
      isLoading: false,
      isError: false,
    });

    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/smart-insights',
    });

    expect(await screen.findByText('EnergyMonitor Premium', { selector: 'h3', exact: true })).toBeInTheDocument();

    // user selects products
    const dongleInput = screen.getByRole('checkbox', { name: 'Eneco Dongle' });
    expect(dongleInput).toBeInTheDocument();

    // dongle should be preselected
    expect(dongleInput).toBeChecked();

    const splitterInput = screen.getByRole('checkbox', { name: 'Splitter' });
    expect(splitterInput).toBeInTheDocument();

    await userEvent.click(splitterInput);

    // user confirms and goes to order overview
    const nextButton = screen.getByRole('button', { name: 'Ga door naar bestellen' });
    expect(nextButton).toBeInTheDocument();

    await userEvent.click(nextButton);

    expect(screen.getByText('Winkelmandje', { selector: 'h3', exact: true })).toBeInTheDocument();

    expect(screen.getByText('EnergyMonitor Premium', { selector: 'span', exact: true })).toBeInTheDocument();
    expect(screen.getByText('Eneco Dongle', { selector: 'span', exact: true })).toBeInTheDocument();
    expect(screen.getByText('Splitter', { selector: 'span', exact: true })).toBeInTheDocument();

    const couponToggle = screen.getByRole('button', { name: 'Kortingscode' });
    expect(couponToggle).toBeInTheDocument();

    await userEvent.click(couponToggle);

    // user confirms and goes to personal data
    const continueButton = screen.getByRole('button', { name: 'Volgende' });
    expect(continueButton).toBeInTheDocument();

    expect(screen.getByRole('textbox', { name: 'Kortingscode' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Toepassen' })).toBeInTheDocument();

    await userEvent.click(continueButton);

    expect(screen.getByText('Jouw gegevens', { selector: 'h3', exact: true })).toBeInTheDocument();

    expect(screen.getByText('5453569645', { selector: 'b', exact: true })).toBeInTheDocument();

    expect(screen.getByText('Persoonsgegevens', { selector: 'h4', exact: true })).toBeInTheDocument();

    expect(screen.getByText('Facturatieadres', { selector: 'h4', exact: true })).toBeInTheDocument();

    expect(screen.getByText('Leveringsadres', { selector: 'h4', exact: true })).toBeInTheDocument();

    // user updates the delivery address
    const invoiceInput = screen.getByRole('checkbox', { name: 'Zelfde als facturatieadres' });
    expect(invoiceInput).toBeInTheDocument();

    await userEvent.click(invoiceInput);

    const streetInput = screen.getByRole('textbox', { name: 'Straat' });
    expect(streetInput).toBeInTheDocument();
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, 'Grote straat');

    const numberInput = screen.getByRole('textbox', { name: 'Nummer' });
    expect(numberInput).toBeInTheDocument();
    await userEvent.clear(numberInput);
    await userEvent.type(numberInput, '185');

    const boxInput = screen.getByRole('textbox', { name: 'Bus' });
    expect(boxInput).toBeInTheDocument();
    await userEvent.clear(boxInput);
    await userEvent.type(boxInput, '2');

    const cityInput = screen.getByRole('textbox', { name: 'Plaats' });
    expect(cityInput).toBeInTheDocument();
    await userEvent.clear(cityInput);
    await userEvent.type(cityInput, 'Antwerpen');

    const postalCodeInput = screen.getByRole('textbox', { name: 'Postcode' });
    expect(postalCodeInput).toBeInTheDocument();
    await userEvent.clear(postalCodeInput);
    await userEvent.type(postalCodeInput, '2060');

    const previousButton = screen.getByRole('button', { name: 'Vorige' });
    expect(previousButton).toBeInTheDocument();

    // user confirms and goes to confirmation
    const continueDataButton = screen.getByRole('button', { name: 'Volgende' });
    expect(continueDataButton).toBeInTheDocument();

    await userEvent.click(continueDataButton);

    // user consents to terms and conditions
    expect(screen.getByText('Jouw bestelling', { selector: 'h3', exact: true })).toBeInTheDocument();

    const termsInput = screen.getByRole('checkbox', {
      name: 'Ik ga akkoord met de voorwaarden en tarieven* en ga een betalingsverplichting met Eneco aan. Tevens machtig ik Eneco, totdat ik deze weer intrek, tot automatische incasso, vanaf de door mij opgegeven rekening, van de verschuldigde maand- en jaarbedragen voor producten en diensten.',
    });
    expect(termsInput).toBeInTheDocument();

    await userEvent.click(termsInput);

    // order should validate
    const payButton = screen.getByRole('button', { name: 'Ga naar de betaalpagina' });
    expect(payButton).toBeInTheDocument();

    await userEvent.click(payButton);

    // user is redirected to external payment portal
  });

  test.skip('should display dialog when user has no dongle history and proceeds with deselecting dongle', async () => {
    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/smart-insights',
    });

    expect(await screen.findByText('Eneco Dongle', { selector: 'h3', exact: true })).toBeInTheDocument();

    // dongle is preselected, user removes it from selection
    const dongleCheckbox = screen.getByTestId('Smart-Dongle');
    expect(dongleCheckbox).toBeInTheDocument();
    await userEvent.click(dongleCheckbox);

    const nextButton = screen.getByRole('button', { name: 'Ga door naar bestellen' });
    expect(nextButton).toBeInTheDocument();
    await userEvent.click(nextButton);

    expect(await screen.findByText('Je hebt geen dongle gekozen', { selector: 'h2', exact: true })).toBeInTheDocument();
  });

  test.skip('should proceed to next step when user has no dongle history and proceeds without deselecting dongle', async () => {
    const validateOrderMock = jest.fn().mockResolvedValue({ success: true });
    (useSubscriptionsValidateOrder as jest.Mock).mockReturnValue({
      send: validateOrderMock,
      isLoading: false,
      isError: false,
    });

    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/smart-insights',
    });

    expect(await screen.findByText('Eneco Dongle', { selector: 'h3', exact: true })).toBeInTheDocument();

    const dongleCheckbox = screen.getByTestId('Smart-Dongle');
    expect(dongleCheckbox).toBeInTheDocument();

    const nextButton = screen.getByRole('button', { name: 'Ga door naar bestellen' });
    expect(nextButton).toBeInTheDocument();
    await userEvent.click(nextButton);

    expect(await screen.findByText('Bestellen', { selector: 'span', exact: true })).toBeInTheDocument();
  });

  test.skip('should display dialog when user has dongle history and proceeds with selecting dongle', async () => {
    (getCustomerProfileBe as jest.MockedFunction<typeof getCustomerProfileBe>).mockImplementation(() =>
      Promise.resolve(mockCustomerProfileWithDongle),
    );

    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/smart-insights',
    });

    expect(await screen.findByText('Eneco Dongle', { selector: 'h3', exact: true })).toBeInTheDocument();

    const dongleCheckbox = screen.getByTestId('Smart-Dongle');
    expect(dongleCheckbox).toBeInTheDocument();
    await userEvent.click(dongleCheckbox);

    const nextButton = screen.getByRole('button', { name: 'Ga door naar bestellen' });
    expect(nextButton).toBeInTheDocument();
    await userEvent.click(nextButton);

    expect(
      await screen.findByText('Je hebt in het verleden al een Eneco Dongle aangekocht', {
        selector: 'h2',
        exact: true,
      }),
    ).toBeInTheDocument();
  });

  test.skip('should proceed to next step when user has dongle history and proceeds without selecting dongle', async () => {
    (getCustomerProfileBe as jest.MockedFunction<typeof getCustomerProfileBe>).mockImplementation(() =>
      Promise.resolve(mockCustomerProfileWithDongle),
    );
    const validateOrderMock = jest.fn().mockResolvedValue({ success: true });
    (useSubscriptionsValidateOrder as jest.Mock).mockReturnValue({
      send: validateOrderMock,
      isLoading: false,
      isError: false,
    });

    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/smart-insights',
    });

    expect(await screen.findByText('Eneco Dongle', { selector: 'h3', exact: true })).toBeInTheDocument();

    const dongleCheckbox = screen.getByTestId('Smart-Dongle');
    expect(dongleCheckbox).toBeInTheDocument();

    const nextButton = screen.getByRole('button', { name: 'Ga door naar bestellen' });
    expect(nextButton).toBeInTheDocument();
    await userEvent.click(nextButton);

    expect(await screen.findByText('Bestellen', { selector: 'span', exact: true })).toBeInTheDocument();
  });

  test.skip('should show a dialog when a user tries to apply multiple promo codes to the same product', async () => {
    (getAvailableProducts as jest.MockedFunction<typeof getAvailableProducts>).mockImplementation(() =>
      Promise.resolve(productsCouponCodesResponse),
    );
    (getCustomerInfo as jest.MockedFunction<typeof getCustomerInfo>).mockImplementation(() =>
      Promise.resolve(customerInfoCouponCodesResponse),
    );
    (getCustomerProfileBe as jest.MockedFunction<typeof getCustomerProfileBe>).mockImplementation(() =>
      Promise.resolve(profileCouponCodesResponse()),
    );

    const validateOrderMock = jest.fn().mockResolvedValue({ success: true });
    (useSubscriptionsValidateOrder as jest.Mock).mockReturnValue({
      send: validateOrderMock,
      isLoading: false,
      isError: false,
    });

    renderApp(ProductPurchaseFlow, {
      mock: insightsMock,
      scope: 'be-eneco-insights',
      path: '/launchpad/dongle',
    });

    // User arrives on the dongle purchase flow
    expect(await screen.findByText('Eneco Dongle', { selector: 'h3', exact: true })).toBeInTheDocument();

    const nextButton = screen.getByRole('button', { name: 'Ga door naar bestellen' });
    expect(nextButton).toBeInTheDocument();
    await userEvent.click(nextButton);

    expect(await screen.findByText('Winkelmandje', { selector: 'h3', exact: true })).toBeInTheDocument();

    const openDiscountButton = screen.getByRole('button', { name: 'Kortingscode' });
    expect(openDiscountButton).toBeInTheDocument();
    await userEvent.click(openDiscountButton);

    //user applies first discount
    const discountInput = screen.getByRole('textbox', { name: 'Kortingscode' });
    expect(discountInput).toBeInTheDocument();
    await userEvent.click(discountInput);

    await userEvent.type(discountInput, 'ENECOVKPT0Y');

    const applyDiscountButton = screen.getByRole('button', { name: 'Toepassen' });
    expect(applyDiscountButton).toBeInTheDocument();

    await userEvent.click(applyDiscountButton);

    expect(validateOrderMock).toHaveBeenCalledTimes(1);
    expect(screen.getByText('ENECOVKPT0Y', { selector: 'span', exact: true })).toBeInTheDocument();

    //user applies an other discount on the same product
    await userEvent.type(discountInput, '50Dongle_general');

    await userEvent.click(applyDiscountButton);
    expect(validateOrderMock).toHaveBeenCalledTimes(2);

    //user sees an alert dialog
    expect(
      await screen.findByText('Wil je deze nieuwe kortingscode toepassen?', {
        selector: 'h2',
        exact: true,
      }),
    ).toBeInTheDocument();

    const cancelButton = screen.getByRole('button', { name: 'Annuleren' });
    expect(cancelButton).toBeInTheDocument();

    await userEvent.click(cancelButton);

    //User applies the new discount again
    await userEvent.type(discountInput, '50Dongle_general');

    await userEvent.click(applyDiscountButton);
    expect(validateOrderMock).toHaveBeenCalledTimes(3);

    expect(
      await screen.findByText('Wil je deze nieuwe kortingscode toepassen?', {
        selector: 'h2',
        exact: true,
      }),
    ).toBeInTheDocument();

    const replaceButton = screen.getByRole('button', { name: 'Nieuwe code toepassen' });
    expect(replaceButton).toBeInTheDocument();

    await userEvent.click(replaceButton);

    //User should see the new coupon code as the applied discount
    expect(screen.getByText('50Dongle_general', { selector: 'span', exact: true })).toBeInTheDocument();
  });

  // !!! Important !!!
  // TODO: When fixing or adding new features, add tests for the concerning feature
  // to make sure the functionality stays consistent and we prevent introducing new bugs
});
