import { FC, useEffect, useState } from 'react';

import { App, AppInfo } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import { Device, DeviceInfo } from '@capacitor/device';
import { ConnectionStatus, Network } from '@capacitor/network';
import { yupResolver } from '@hookform/resolvers/yup';
import { SubmitHandler, useForm } from 'react-hook-form';
import * as yup from 'yup';

import { fileToBase64 } from '@common/file';
import logger from '@common/log';
import RichText from '@components/RichText/RichText';
import NativeBottomNavigationWrapper from '@custom-components/native/NativeBottomNavigationWrapper';
import { useFormPostEloquaForm } from '@dc/hooks';
import { I18nProvider, useTranslation } from '@i18n';
import { useCustomerProfileContext } from '@native-components/components/wrappers/CustomerProfileProvider/CustomerProfileProvider';
import { useContent } from '@sitecore/common';
import { ReportProblemRendering } from '@sitecore/types/ReportProblem';
import {
  Box,
  Button,
  Card,
  FilePicker,
  Form,
  Stack,
  Stretch,
  TextArea,
  Text,
  Skeleton,
  NotificationBox,
  Checkbox,
} from '@sparky';

import ReportProblemSuccess from './ReportProblemSuccess';

type FormValues = {
  technicalProblem: string;
  consent: boolean;
};

const reportProblemSchema = yup.object({
  technicalProblem: yup.string().required(),
  consent: yup.boolean().oneOf([true]),
});

const ReportProblemComponent: FC = () => {
  const { fields } = useContent<ReportProblemRendering>();
  const { t } = useTranslation();

  const { send, isSuccess, isError, isLoading } = useFormPostEloquaForm();
  const { data: profileData, isLoading: isLoadingProfile } = useCustomerProfileContext();

  const [files, setFiles] = useState<File[]>([]);
  const [hasFileError, setHasFileError] = useState<boolean>(false);

  const [network, setNetwork] = useState<ConnectionStatus | null>(null);
  const [device, setDevice] = useState<DeviceInfo | null>(null);
  const [appDetails, setAppDetails] = useState<AppInfo | null>(null);

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<FormValues>({
    mode: 'onChange',
    resolver: yupResolver(reportProblemSchema),
  });

  useEffect(() => {
    const fetchDeviceInfo = async () => {
      const networkInfo = await Network.getStatus();
      setNetwork(networkInfo);

      const deviceInfo = await Device.getInfo();
      setDevice(deviceInfo);

      if (Capacitor.isNativePlatform()) {
        const appInfo = await App.getInfo();
        setAppDetails(appInfo);
      }
    };

    fetchDeviceInfo();
  }, []);

  const submitForm: SubmitHandler<FormValues> = async values => {
    try {
      const attachments = files
        ? await Promise.all(
            files.slice(0, 5).map(async file => {
              const content = await fileToBase64(file);
              return {
                filename: file.name,
                content: content,
              };
            }),
          )
        : undefined;

      const requestBody = {
        data: {
          fields: {
            EmailAddress: profileData?.contact?.emailAddress || 'missing',
            CustomerFirstName: profileData?.person?.firstName || 'missing',
            CustomerLastName: profileData?.person?.surname || 'missing',
            LegalDisclaimer: values.consent ? 'yes' : 'no',
            TechnicalProblem: values.technicalProblem,
            DateStamp: new Date().toDateString(),
            AppVersion: `${appDetails?.version} - ${process.env.commitSHA ?? ''}`,
            BrowserVersion: window.navigator.userAgent || 'n/a',
            HardwareInfo: JSON.stringify(device),
            InternetType: network?.connectionType || 'unknown',
          },
          attachments: attachments,
        },
      };

      await send({
        formName: 'SmartMeterAppBugs',
        requestBody: requestBody,
      });
    } catch (error) {
      logger.error('5Q4PhL', 'Failed to submit report a problem form', { error });
    }
  };

  if (isLoadingProfile) {
    return <Skeleton height={400} />;
  }

  if (isSuccess) {
    return (
      <Stretch height>
        <ReportProblemSuccess />
      </Stretch>
    );
  }

  return (
    <Stretch height>
      <Form onSubmit={handleSubmit(submitForm)}>
        <Stretch height>
          <Stack>
            <Stack.Item grow>
              <Box paddingBottom="4">
                <Stack gap="4">
                  <Card>
                    <Box padding="4">
                      <Stack gap="4">
                        <TextArea
                          {...register('technicalProblem')}
                          required
                          label={fields.reportLabel.value}
                          placeholder={fields.reportPlaceholderText.value}
                          rows={7}
                          error={errors.technicalProblem && fields.reportErrorText.value}
                        />
                        <Stack gap="2">
                          <Text size="BodyS" weight="bold">
                            {fields.uploadFilesLabel.value}
                          </Text>
                          <FilePicker
                            name="attachments"
                            accept={['JPEG', 'JPG', 'PNG']}
                            files={files}
                            hasError={hasFileError}
                            setHasError={setHasFileError}
                            setFiles={setFiles}
                            maxFileSize={1}
                            maxTotalSize={10}
                            maxAmount={5}
                            headingLevel="h6"
                          />
                        </Stack>
                      </Stack>
                    </Box>
                  </Card>

                  <Checkbox
                    {...register('consent')}
                    label={fields.consentFormField.value.label}
                    error={errors.consent && fields.consentFormField.value.requiredMessage}
                  />
                </Stack>
              </Box>
            </Stack.Item>

            <NativeBottomNavigationWrapper>
              <Stack gap="4">
                {isError && (
                  <NotificationBox
                    isAlert
                    title={t('error.title')}
                    text={<RichText html={t('error.message')} />}
                    variant="error"
                  />
                )}
                <Button type="submit" action="primary" size="compact" isLoading={isLoading}>
                  {fields.buttonLabel.value}
                </Button>
              </Stack>
            </NativeBottomNavigationWrapper>
          </Stack>
        </Stretch>
      </Form>
    </Stretch>
  );
};

const ReportProblem = () => {
  return (
    <I18nProvider dictionary={locale => import(`./content/${locale}.json`)}>
      <ReportProblemComponent />
    </I18nProvider>
  );
};

export default ReportProblem;
