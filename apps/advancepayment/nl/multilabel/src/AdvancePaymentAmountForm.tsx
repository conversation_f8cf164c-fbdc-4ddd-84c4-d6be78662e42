import React, { FC, useEffect, useRef } from 'react';

import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';

import RichText from '@components/RichText/RichText';
import { TrackedDialog } from '@components/TrackedDialog/TrackedDialog';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import { useRedirectAndNotify } from '@components-next/Notification/Notification';
import { useFinancialsGetAdvancePaymentAdviceV2, useFinancialsPutAdvancePaymentNl } from '@dc/hooks';
import useDC from '@dc/useDC';
import { useRouter } from '@dxp-next';
import { I18nProvider, useFormatter, useTranslation } from '@i18n';
import { useLinkComponent } from '@link';
import { Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2Limits } from '@monorepo-types/dc';
import { useContent } from '@sitecore/common';
import { AdvancePaymentAmountFormRendering } from '@sitecore/types/AdvancePaymentAmountForm';
import { Bucket, Button, ButtonLink, Form, Grid, InputStepper, Skeleton, Stack, Text, TextLink } from '@sparky';
import { useTracking } from '@tracking';

import { getAdvancePaymentAmountSchema } from './AdvancePaymentAmountForm.schema';
import { AdvancePaymentAmountStatusError } from './AdvancePaymentUpdateStatusError';
import { AdvicePaymentAmountErrorNotification } from './AdvicePaymentAmountErrorNotification';
import { AdvancePaymentAmountFormError } from './components/AdvancePaymentAmountFormError';
import { useAdvancePaymentAmountValidation } from './hooks/useAdvancePaymentAmountValidation';

export type FormValues = {
  amount: number;
};

const AdvancePaymentAmountForm = () => {
  const { data: advancePaymentAdvice, isValidating: isLoading } = useFinancialsGetAdvancePaymentAdviceV2();
  const { label } = useDC();
  if (isLoading || !advancePaymentAdvice) return <Skeleton height={600} />;

  if (advancePaymentAdvice?.limits?.updateStatus === 'canUpdate') {
    return (
      <I18nProvider dictionary={locale => import(`./content/${label}.${locale}.json`)}>
        <AdvancePaymentAmountFormComponent
          current={advancePaymentAdvice.current}
          advancePaymentLimits={advancePaymentAdvice.limits}
          isIncreaseWillBePermanent={advancePaymentAdvice.increaseWillBePermanentAboveMinimumRange}
        />
      </I18nProvider>
    );
  }

  return <AdvancePaymentAmountStatusError />;
};

const AdvancePaymentAmountFormComponent: FC<{
  current: number;
  advancePaymentLimits: Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2Limits;
  isIncreaseWillBePermanent: boolean;
}> = ({ current, advancePaymentLimits, isIncreaseWillBePermanent }) => {
  const { format } = useFormatter();
  const Link = useLinkComponent();
  const { fields, f } = useContent<AdvancePaymentAmountFormRendering>();
  const { t } = useTranslation();
  const redirectAndNotify = useRedirectAndNotify();
  const { mutate } = useFinancialsGetAdvancePaymentAdviceV2();
  const { activePath } = useRouter();
  const { trackFormInteractionStart, trackFormInteractionCompleted, trackPageView } = useTracking();
  const formRef = useRef<HTMLFormElement>(null);

  const { send, isLoading, isSuccess, error } = useFinancialsPutAdvancePaymentNl();

  const errorCode = error?.firstError?.code?.toUpperCase();

  const { register, handleSubmit, formState, getValues, control, reset } = useForm<{
    amount: number;
  }>({
    mode: 'onChange',
    defaultValues: {
      amount: current,
    },
    resolver:
      advancePaymentLimits?.minimumRange && advancePaymentLimits.maximumRange
        ? yupResolver(
            getAdvancePaymentAmountSchema(advancePaymentLimits?.minimumRange, advancePaymentLimits?.maximumRange),
          )
        : undefined,
  });

  const { notification, errorMessage } = useAdvancePaymentAmountValidation({
    control,
    advancePaymentLimits,
  });

  // Hint for input stepper showing the min advance payment amount possible based on the advice
  const hint = t('advancePaymentAmountForm.hint', {
    min: advancePaymentLimits?.minimumRange || 0,
  });

  const onSubmit = ({ amount }: FormValues) => {
    send({ amount });
  };

  // Reset `isSubmitted` and `keepSubmitCount` to reset notifications and errors
  const resetFormState = () => {
    reset(
      {},
      {
        keepDirtyValues: true,
        keepErrors: true,
        keepDirty: true,
        keepValues: true,
        keepDefaultValues: true,
        keepIsSubmitted: false,
        keepTouched: true,
        keepIsValid: true,
        keepSubmitCount: false,
      },
    );
  };

  useEffect(() => {
    if (isSuccess) {
      mutate();

      trackFormInteractionCompleted();

      trackPageView({
        pageName: `${activePath}success`,
        previousPage: window.location.origin + activePath,
      });

      redirectAndNotify({
        variant: 'success',
        text: fields.formFeedbackSuccessText.value,
        route: fields.cancelLink.value.href,
      });
    }
  }, [
    isSuccess,
    getValues,
    mutate,
    redirectAndNotify,
    fields.cancelLink.value.href,
    fields.formFeedbackSuccessText.value,
    activePath,
    trackFormInteractionCompleted,
    trackPageView,
  ]);

  return (
    <Form ref={formRef} onFocus={trackFormInteractionStart} onSubmit={handleSubmit(onSubmit)}>
      <Bucket title={fields.title.value}>
        <Bucket.Content>
          <Stack gap="2">
            <Stack direction="row" gap="2">
              <Text>{fields.currentAmountText.value}</Text>
              <Text weight="bold">{t('advancePaymentAmountForm.currentAmount', { current })}</Text>
            </Stack>
            <Text>{fields.content.value}</Text>
          </Stack>
          <Grid
            columns={{
              initial: '1',
              md: '2',
            }}>
            <InputStepper
              {...register('amount', { onChange: () => resetFormState() })}
              isDisabled={isLoading}
              defaultValue={current}
              label={fields.advancePaymentAmountLabel.value}
              min={advancePaymentLimits?.minimumRange}
              max={advancePaymentLimits?.maximumRange}
              hint={hint}
              error={errorMessage}
            />
          </Grid>
          {notification && !formState.isSubmitted && (
            <Stack.Item>
              <TrackedNotificationBox
                isAlert={false}
                variant="info"
                label={notification.title || notification.text}
                title={notification.title}
                text={
                  <RichText
                    html={notification.text}
                    replacements={
                      notification?.dialogContent
                        ? {
                            button: props => (
                              <TrackedDialog
                                title={notification?.dialogContent?.title}
                                trigger={<TextLink>{props.children}</TextLink>}>
                                <Stack gap="4">
                                  <Text>{notification?.dialogContent?.text}</Text>
                                </Stack>
                              </TrackedDialog>
                            ),
                          }
                        : undefined
                    }
                  />
                }
              />
            </Stack.Item>
          )}
          {isIncreaseWillBePermanent && formState.isDirty && (
            <Stack.Item>
              <TrackedNotificationBox
                isAlert={false}
                variant="warning"
                label={fields?.increaseWillBePermanentTitle?.value || fields?.increaseWillBePermanentDescription?.value}
                title={fields?.increaseWillBePermanentTitle?.value}
                text={
                  <RichText
                    html={format(fields?.increaseWillBePermanentDescription?.value, {
                      minimumRange: advancePaymentLimits?.minimumRange,
                    })}
                  />
                }
              />
            </Stack.Item>
          )}
        </Bucket.Content>
        <Bucket.Footer>
          <Bucket.Actions>
            <Button isLoading={isLoading} type="submit">
              {fields.saveButtonText.value}
            </Button>
            {!isLoading && (
              <Link href={fields.cancelLink.value.href} linkType={fields.cancelLink.value.linktype}>
                <ButtonLink action="secondary">
                  {fields.cancelLink.value.text || t('advancePaymentAmountForm.buttons.cancel')}
                </ButtonLink>
              </Link>
            )}
          </Bucket.Actions>
          {error && (
            <Stack.Item>
              <AdvicePaymentAmountErrorNotification
                errorCode={errorCode}
                trackError={true}
                unknownErrorText={f('formFeedbackDefaultErrorDescription.value')}
                estimatedMeterReadingsErrorText={f('formFeedbackEstimatedMeterReadingsErrorDescription.value')}
                multipleAccountsErrorText={f('formFeedbackMultipleAccountsErrorDescription.value')}
              />
            </Stack.Item>
          )}
          {advancePaymentLimits && !formState.isValid && formState.isSubmitted && (
            <AdvancePaymentAmountFormError
              control={control}
              minimumRange={advancePaymentLimits.minimumRange}
              maximumRange={advancePaymentLimits.maximumRange}
              fields={fields}
            />
          )}
        </Bucket.Footer>
      </Bucket>
    </Form>
  );
};

export default AdvancePaymentAmountForm;
