import { useFinancialsGetAdvancePaymentAdviceV2 } from '@dc/hooks';
import {
  DC_Domain_Models_Financials_AdvancePaymentAdviceStatus,
  DC_Financials_Client_Models_AdvancePaymentUpdateStatus,
  Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2,
  Financials_AdvancePaymentAdviceV2_AdviceStatusToggles,
  Financials_ForecastRange,
} from '@monorepo-types/dc';

export type AdvancedPaymentAmountAdvice = {
  isAboveSafePaybackRange: boolean;
  advicePaymentAmount?: number;
  adviceStatus?: DC_Domain_Models_Financials_AdvancePaymentAdviceStatus;
  isBelowSafeGetBackRange: boolean;
  canUpdate: boolean;
  cashBackAmount?: Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2['cashBackAmount'];
  currentAdvancePaymentAmount?: Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2['current'];
  forecastRange?: Financials_ForecastRange;
  hasCashback: boolean;
  isAdviceStatusOK: boolean;
  isAdviceStatusUpdate: boolean;
  isError: boolean;
  isLoading: boolean;
  isMetersCombination: boolean;
  showAdvice: Financials_AdvancePaymentAdviceV2_AdviceStatusToggles['showAdvice'];
  showForecast: boolean;
  updateStatus?: DC_Financials_Client_Models_AdvancePaymentUpdateStatus;
  yearNoteDate?: Financials_AdvancePaymentAdviceV2_AdvancePaymentAdviceV2['yearNoteDate'];
};

export const useAdvancePaymentAmountAdvice = (): AdvancedPaymentAmountAdvice => {
  const { data, isValidating: isLoading, error } = useFinancialsGetAdvancePaymentAdviceV2();
  const isError = (!isLoading && !data) || error;

  const updateStatus = data?.limits?.updateStatus;
  const canUpdate = updateStatus === 'canUpdate';
  const currentAdvancePaymentAmount = data?.current;
  const advicePaymentAmount = data?.advice;
  const yearNoteDate = data?.yearNoteDate;
  const yearNoteForecast = data?.forecastRange?.min || 0;
  const isBelowSafeGetBackRange = yearNoteForecast <= -25;
  const isAboveSafePaybackRange = yearNoteForecast >= 25;
  const isAdviceStatusOK = data?.adviceStatus === 'ok';
  const isAdviceStatusUpdate =
    data?.adviceStatus === 'updateAdvice' ||
    data?.adviceStatus === 'adviceTooOld' ||
    data?.adviceStatus === 'adviceFromLastYear';
  const adviceStatus = data?.adviceStatus;
  const isMetersCombination = data?.metersCombination === 'combination';
  const showAdvice = !!data?.statusToggle?.showAdvice;
  const showForecast = !!data?.statusToggle?.showForecast && (isBelowSafeGetBackRange || isAboveSafePaybackRange);
  const forecastRange = data?.forecastRange;
  const cashBackAmount = data?.cashBackAmount;
  const hasCashback = !!cashBackAmount && data.cashBackAmount > 0;

  return {
    isAboveSafePaybackRange,
    advicePaymentAmount,
    adviceStatus,
    isBelowSafeGetBackRange,
    canUpdate,
    cashBackAmount,
    currentAdvancePaymentAmount,
    forecastRange,
    hasCashback,
    isAdviceStatusOK,
    isAdviceStatusUpdate,
    isError,
    isLoading,
    isMetersCombination,
    showAdvice,
    showForecast,
    updateStatus,
    yearNoteDate,
  };
};
