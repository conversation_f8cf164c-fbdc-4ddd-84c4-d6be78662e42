import React from 'react';

import { render, screen, waitFor, waitForElementToBeRemoved } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { testDialog } from '@jest-tools/helpers/sparky';
import renderApp from '@jest-tools/renderApp';
import { TestAppProviders } from '@jest-tools/TestAppProviders';
import selfserviceMock from '@mocks/sitecore/containers/nl/eneco/selfservice';
import advancePaymentAmountForm from 'mocks/sitecore/apps/advancepayment/nl/multilabel/advancePaymentAmountForm';

import AdvancePaymentAmountForm from './AdvancePaymentAmountForm';

const mockOptions = {
  mock: selfserviceMock,
  scope: 'nl-eneco-selfservice',
  path: '/mijn-eneco/termijnbedrag/aanpassen/',
  customerId: '2029587',
};

describe('Advance payment amount form (aka "installment amount")', () => {
  it('should render form page', async () => {
    const user = userEvent.setup();

    renderApp(AdvancePaymentAmountForm, mockOptions);
    expect(await screen.findByText(/kunt je termijnbedrag altijd verhogen of juist verlagen/)).toBeInTheDocument();
    expect(await screen.findByText(/Het minimale termijnbedrag is €45/)).toBeInTheDocument();

    // Test initial form value:
    const installmentAmountInput = screen.getByRole('spinbutton', { name: 'Termijnbedrag' });
    expect(installmentAmountInput).toHaveValue('185');
    const plusButton = screen.getByRole('button', { name: 'Verhogen Termijnbedrag' });

    await user.click(plusButton);

    expect(installmentAmountInput).toHaveValue('186');

    // Change value to "too high":
    await user.clear(installmentAmountInput);
    await user.type(installmentAmountInput, '336');
    const amountTooHighCopy = await screen.findByText(/Het ingevoerde bedrag is te hoog/);
    expect(amountTooHighCopy).toBeInTheDocument();

    // Change value to "high", but within allowed range:
    await user.clear(installmentAmountInput);
    await user.type(installmentAmountInput, '334');
    await waitForElementToBeRemoved(() => screen.queryByText(/Het ingevoerde bedrag is te hoog/));
    const amountHighCopy = await screen.findByText(/Let op! Wij adviseren om niet hoger dan € 300 te kiezen/);
    expect(amountHighCopy).toBeInTheDocument();

    // Change value to "too low":
    await user.clear(installmentAmountInput);
    await user.type(installmentAmountInput, '44');
    // Make sure `changeWillBePermanent` notification is not rendered
    expect(screen.queryByText(/Belangrijk/)).not.toBeInTheDocument();
    const amountTooLowCopy = await screen.findByText(/Het ingevoerde bedrag is te laag/);
    expect(amountTooLowCopy).toBeInTheDocument();

    await testDialog(
      user,
      screen.getByRole('button', { name: /Lees meer/i }),
      /Je maandelijkse termijnbedrag is berekend op basis van jouw persoonlijke energietarieven en je verwachte energieverbruik door het jaar heen. Als je termijnbedrag te laag is, is de kans groter dat je op de jaarnota veel moet bijbetalen. Om dit te voorkomen, is er een minimaal termijnbedrag van € 45 voor je berekend./,
      'Waarom een minimaal termijnbedrag?',
    );

    // Change value to "low", but within allowed range:
    await user.clear(installmentAmountInput);
    await user.type(installmentAmountInput, '46');
    await waitForElementToBeRemoved(() => screen.queryByText(/Het ingevoerde bedrag is te laag/));
    const amountLowCopy = await screen.findByText(/Let op! Wij adviseren om niet lager dan € 75 te kiezen/);
    expect(amountLowCopy).toBeInTheDocument();
  });

  // NOTE: saving cannot be tested since we only mounted a single feature component in this test, not a page with
  // routing. Full integration test is done via Cypress E2E test(s).

  it('should show a known 400 error message after submitting the form', async () => {
    const user = userEvent.setup();

    render(
      <TestAppProviders customerId="400" rendering={advancePaymentAmountForm('/mijn-eneco')}>
        <AdvancePaymentAmountForm />
      </TestAppProviders>,
    );

    expect(await screen.findByText(/Termijnbedrag aanpassen/)).toBeInTheDocument();

    const submitButton = screen.getByRole('button', { name: 'Opslaan' });
    await user.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          /Je kan je termijnbedrag niet verlagen, omdat je laatste jaarlijkse factuur gebaseerd is op geschatte meterstanden/,
        ),
      ).toBeInTheDocument();
    });
  });

  it('If the increase will be permanent, a notification must be shown to user that the change is permanent', async () => {
    renderApp(AdvancePaymentAmountForm, { ...mockOptions, customerId: '2029517' });
    const user = userEvent.setup();

    expect(await screen.findByText(/Termijnbedrag aanpassen/)).toBeInTheDocument();
    const installmentAmountInput = screen.getByRole('spinbutton', { name: 'Termijnbedrag' });
    expect(installmentAmountInput).toHaveValue('5');

    // Make sure `changeWillBePermanent` notification is not rendered before field is dirty
    expect(screen.queryByText(/Belangrijk/)).not.toBeInTheDocument();
    await user.clear(installmentAmountInput);
    await user.type(installmentAmountInput, '46');
    expect(
      screen.getByText(
        /Na deze wijziging kan je je termijnbedrag niet meer lager instellen dan €10 via de app of Mijn Eneco./,
      ),
    ).toBeInTheDocument();
    expect(screen.getByText(/Belangrijk/)).toBeInTheDocument();
  });
});
