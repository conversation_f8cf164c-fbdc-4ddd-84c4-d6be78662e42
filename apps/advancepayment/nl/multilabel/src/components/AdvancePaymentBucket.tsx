import React, { FC } from 'react';

import RichText from '@components/RichText/RichText';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import ComponentError from '@components/ui/ComponentError/ComponentError';
import { useFormatter, useTranslation } from '@i18n';
import { useLinkComponent } from '@link';
import { useContent } from '@sitecore/common';
import { AdvancePaymentAmountRendering } from '@sitecore/types/AdvancePaymentAmount';
import { Bucket, Stack, Text, ButtonLink } from '@sparky';

import { useAdvancePaymentAmountAdvice } from '../hooks/useAdvancePaymentAmountAdvice';
import { getPaymentLimitsCopy } from '../utils/GetPaymentLimitsCopy';

const AdvancePaymentBucket: FC = () => {
  const Link = useLinkComponent();
  const { t } = useTranslation();
  const { format } = useFormatter();
  const { fields, f } = useContent<AdvancePaymentAmountRendering>();

  const { yearNoteDate, updateStatus, currentAdvancePaymentAmount } = useAdvancePaymentAmountAdvice();

  const termAmountNotification = getPaymentLimitsCopy({ updateStatus, yearNoteDate, format, f });

  if (!(typeof currentAdvancePaymentAmount === 'number'))
    return <ComponentError message={t('advancePaymentAmount.noCurrentAmountErrorText')} />;

  return (
    <Bucket title={fields.advancePaymentBucketTitle.value}>
      <Bucket.Content>
        <Stack gap="4">
          <div>
            <Text size="BodyL">{fields.paymentOkTitle.value}</Text>
            <Text size="BodyL" weight="bold">
              {t('advancePaymentAmount.paymentFine.currentAdvancePaymentAmount', {
                current: currentAdvancePaymentAmount,
              })}
            </Text>
          </div>

          {termAmountNotification && (
            <Stack.Item>
              <TrackedNotificationBox
                isAlert={false}
                variant="info"
                title={termAmountNotification.title}
                text={<RichText html={termAmountNotification.text} />}
              />
            </Stack.Item>
          )}

          {fields.editLink.value.href && !termAmountNotification && (
            <Stack direction={{ initial: 'column', lg: 'row' }}>
              <Link href={fields.editLink.value.href} linkType={fields.editLink.value.linktype}>
                <ButtonLink>{fields.editLink.value.text}</ButtonLink>
              </Link>
            </Stack>
          )}
        </Stack>
      </Bucket.Content>
    </Bucket>
  );
};

export default AdvancePaymentBucket;
