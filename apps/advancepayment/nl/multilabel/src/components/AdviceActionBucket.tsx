import React, { FC, useEffect } from 'react';

import RichText from '@components/RichText/RichText';
import { TrackedNotificationBox } from '@components/TrackedNotificationBox/TrackedNotificationBox';
import { useSetNotification } from '@components-next/Notification/Notification';
import { useFinancialsGetAdvancePaymentAdviceV2, useFinancialsPutAdvancePaymentNl } from '@dc/hooks';
import { useRouter } from '@dxp-next';
import { useFormatter, useTranslation } from '@i18n';
import { useLinkComponent } from '@link';
import { useContent } from '@sitecore/common';
import { AdvancePaymentAmountRendering } from '@sitecore/types/AdvancePaymentAmount';
import { Bucket, Button, Stack, TextLink, Dialog, ButtonLink } from '@sparky';
import { useTracking } from '@tracking';

import AdvancePaymentAdviceBuildUpDialogContent from './AdvancePaymentAdviceBuildUpDialogContent';
import AdviceCurrentVsRecommended from './AdviceCurrentVsRecommended';
import { AdvicePaymentAmountErrorNotification } from '../AdvicePaymentAmountErrorNotification';
import { AdvancedPaymentAmountAdvice, useAdvancePaymentAmountAdvice } from '../hooks/useAdvancePaymentAmountAdvice';
import { getPaymentLimitsCopy } from '../utils/GetPaymentLimitsCopy';

const getAdviceDescriptionCopy = ({
  forecastRange,
  fields,
}: {
  forecastRange: AdvancedPaymentAmountAdvice['forecastRange'];
  fields: AdvancePaymentAmountRendering['fields'];
}) => {
  if (!forecastRange) return;

  const getBack = forecastRange.min < 0;
  const payBack = forecastRange.min > 0;

  if (getBack) {
    return {
      description: fields.adviceDecreaseDescription.value,
      cta: fields.adviceDecreaseCta.value,
    };
  }

  if (payBack) {
    return {
      description: fields.adviceIncreaseDescription.value,
      cta: fields.adviceIncreaseCta.value,
    };
  }
};

const AdviceActionBucket: FC = () => {
  const Link = useLinkComponent();
  const { t } = useTranslation();
  const { activePath } = useRouter();
  const { trackPageView } = useTracking();
  const { format } = useFormatter();
  const { fields, f } = useContent<AdvancePaymentAmountRendering>();
  const { send, isLoading, isError, isSuccess, error } = useFinancialsPutAdvancePaymentNl();
  const { mutate } = useFinancialsGetAdvancePaymentAdviceV2();
  const setNotification = useSetNotification();

  const { yearNoteDate, advicePaymentAmount, currentAdvancePaymentAmount, forecastRange, updateStatus } =
    useAdvancePaymentAmountAdvice();

  const termAmountNotification = getPaymentLimitsCopy({
    updateStatus,
    yearNoteDate,
    format,
    f,
  });
  const adviceDescription = getAdviceDescriptionCopy({ forecastRange, fields });

  const errorCode = error?.firstError?.code?.toUpperCase();

  const onApplyAdviceClick = () => {
    send({ amount: advicePaymentAmount ?? 0 });
  };

  useEffect(() => {
    if (!isSuccess) return;

    mutate();

    setNotification({
      variant: 'success',
      text: t('advancePaymentAmount.adviceTakenOverText', { current: advicePaymentAmount ?? 0 }),
    });
    window.scrollTo(0, 0);

    trackPageView({
      pageName: `${activePath}success`,
      previousPage: window.location.origin + activePath,
    });
  }, [advicePaymentAmount, t, isSuccess, mutate, setNotification, activePath, trackPageView]);

  return (
    <Bucket title={fields.paymentAdviceTitle.value}>
      <Bucket.Content>
        <Stack gap="6">
          <Stack.Item>
            <AdviceCurrentVsRecommended current={currentAdvancePaymentAmount} recommended={advicePaymentAmount} />
          </Stack.Item>
          {adviceDescription && !termAmountNotification && (
            <Stack.Item>
              <RichText html={adviceDescription.description} />
            </Stack.Item>
          )}

          {termAmountNotification ? (
            <Stack>
              <TrackedNotificationBox
                isAlert={false}
                variant="info"
                title={termAmountNotification.title}
                text={<RichText html={termAmountNotification.text} />}
              />
            </Stack>
          ) : (
            <>
              {adviceDescription && (
                <Stack.Item>
                  <Button onClick={onApplyAdviceClick} isLoading={isLoading}>
                    {adviceDescription.cta}
                  </Button>
                </Stack.Item>
              )}

              {isError && (
                <Stack.Item>
                  <AdvicePaymentAmountErrorNotification
                    errorCode={errorCode}
                    unknownErrorText={fields.adviceTakeOverErrorText.value}
                    multipleAccountsErrorText={fields.adviceTakeOverMultipleAccountsErrorText.value}
                    estimatedMeterReadingsErrorText={fields.adviceTakeOverEstimatedMeterReadingsErrorText.value}
                  />
                </Stack.Item>
              )}

              {fields.adviceBucketEditLink.value.href && (
                <Stack direction={{ initial: 'column', lg: 'row' }}>
                  <Link
                    href={fields.adviceBucketEditLink.value.href}
                    linkType={fields.adviceBucketEditLink.value.linktype}>
                    <ButtonLink>{fields.adviceBucketEditLink.value.text}</ButtonLink>
                  </Link>
                </Stack>
              )}
              {fields.adviceBuildUpDialog?.value.triggerText && (
                <Dialog trigger={<TextLink emphasis="high">{fields.adviceBuildUpDialog?.value.triggerText}</TextLink>}>
                  <AdvancePaymentAdviceBuildUpDialogContent />
                </Dialog>
              )}
            </>
          )}
        </Stack>
      </Bucket.Content>
    </Bucket>
  );
};

export default AdviceActionBucket;
